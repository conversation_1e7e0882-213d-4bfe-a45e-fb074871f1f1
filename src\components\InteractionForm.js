import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Appbar,
  TextInput,
  Button,
  SegmentedButtons,
  Text,
  Card,
  useTheme,
  Snackbar
} from 'react-native-paper';
import { INTERACTION_TYPES } from '../constants/dunbarCircles';

export default function InteractionForm({
  contact,
  initialData = null,
  onSubmit,
  submitButtonText = 'Submit',
  headerTitle = 'Interaction',
  contactSubtitle = 'Log your interaction with this contact',
  navigation
}) {
  const theme = useTheme();
  const styles = createStyles(theme);

  // Initialize form data - use initialData if provided, otherwise use defaults
  const getInitialFormData = () => {
    if (initialData) {
      return {
        type: initialData.type || 'other',
        date: initialData.date ? new Date(initialData.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        notes: initialData.notes || '',
        duration: initialData.duration ? initialData.duration.toString() : '',
        quality: initialData.quality || 'good',
        mood: initialData.mood || 'positive'
      };
    }
    return {
      type: 'other',
      date: new Date().toISOString().split('T')[0],
      notes: '',
      duration: '',
      quality: 'good',
      mood: 'positive'
    };
  };

  const [formData, setFormData] = useState(getInitialFormData());
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Interaction type options for SegmentedButtons
  const typeOptions = Object.keys(INTERACTION_TYPES).map(key => ({
    value: key,
    label: INTERACTION_TYPES[key].name,
    icon: INTERACTION_TYPES[key].icon
  }));

  // Quality options
  const qualityOptions = [
    { value: 'brief', label: 'Brief' },
    { value: 'good', label: 'Good' },
    { value: 'deep', label: 'Deep' }
  ];

  // Mood options
  const moodOptions = [
    { value: 'positive', label: 'Positive' },
    { value: 'neutral', label: 'Neutral' },
    { value: 'challenging', label: 'Challenging' }
  ];

  // Form validation
  const validateForm = () => {
    const newErrors = {};
    
    // Validate date
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!formData.date) {
      newErrors.date = 'Date is required';
    } else if (!dateRegex.test(formData.date)) {
      newErrors.date = 'Please enter date in YYYY-MM-DD format';
    } else {
      const date = new Date(formData.date);
      if (isNaN(date.getTime())) {
        newErrors.date = 'Please enter a valid date';
      }
    }

    // Validate duration (if provided)
    if (formData.duration && (isNaN(formData.duration) || parseInt(formData.duration) <= 0)) {
      newErrors.duration = 'Duration must be a positive number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const formattedData = {
        type: formData.type,
        date: new Date(formData.date),
        duration: formData.duration ? parseInt(formData.duration) : null,
        quality: formData.quality,
        mood: formData.mood,
        notes: formData.notes
      };

      const result = await onSubmit(formattedData);
      
      // Show success message
      setSnackbarMessage(result.message || 'Success!');
      setSnackbarVisible(true);

      // Navigate back after a short delay to let user see the toast
      setTimeout(() => {
        navigation.goBack();
      }, 500);
    } catch (error) {
      console.error('Error submitting form:', error);
      setSnackbarMessage('An error occurred. Please try again.');
      setSnackbarVisible(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={headerTitle} />
      </Appbar.Header>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.contactCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.contactName}>
              {contact.name}
            </Text>
            <Text variant="bodyMedium" style={styles.contactSubtitle}>
              {contactSubtitle}
            </Text>
          </Card.Content>
        </Card>

        <Card style={styles.formCard}>
          <Card.Content>
            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Interaction Type
              </Text>
              <SegmentedButtons
                value={formData.type}
                onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
                buttons={typeOptions}
                style={styles.segmentedButtons}
              />
            </View>

            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Date
              </Text>
              <TextInput
                mode="outlined"
                value={formData.date}
                onChangeText={(text) => setFormData(prev => ({ ...prev, date: text }))}
                error={!!errors.date}
                placeholder="YYYY-MM-DD"
                left={<TextInput.Icon icon="calendar" />}
                style={styles.input}
              />
              {errors.date && <Text style={styles.errorText}>{errors.date}</Text>}
            </View>

            {(formData.type === 'call' || formData.type === 'meeting') && (
              <View style={styles.section}>
                <Text variant="titleMedium" style={styles.sectionTitle}>
                  Duration (minutes)
                </Text>
                <TextInput
                  mode="outlined"
                  value={formData.duration}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, duration: text }))}
                  error={!!errors.duration}
                  placeholder="e.g., 30"
                  keyboardType="numeric"
                  left={<TextInput.Icon icon="clock-outline" />}
                  style={styles.input}
                />
                {errors.duration && <Text style={styles.errorText}>{errors.duration}</Text>}
              </View>
            )}

            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Quality
              </Text>
              <SegmentedButtons
                value={formData.quality}
                onValueChange={(value) => setFormData(prev => ({ ...prev, quality: value }))}
                buttons={qualityOptions}
                style={styles.segmentedButtons}
              />
            </View>

            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Mood
              </Text>
              <SegmentedButtons
                value={formData.mood}
                onValueChange={(value) => setFormData(prev => ({ ...prev, mood: value }))}
                buttons={moodOptions}
                style={styles.segmentedButtons}
              />
            </View>

            <View style={styles.section}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Notes (Optional)
              </Text>
              <TextInput
                mode="outlined"
                value={formData.notes}
                onChangeText={(text) => setFormData(prev => ({ ...prev, notes: text }))}
                placeholder="Add any notes about this interaction..."
                multiline
                numberOfLines={3}
                left={<TextInput.Icon icon="note-text" />}
                style={styles.input}
              />
            </View>

            <Button
              mode="contained"
              onPress={handleSubmit}
              loading={loading}
              disabled={loading}
              style={styles.submitButton}
              icon="check"
            >
              {submitButtonText}
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: 'OK',
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  contactCard: {
    marginBottom: 16,
    elevation: 2,
  },
  contactName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  contactSubtitle: {
    opacity: 0.7,
  },
  formCard: {
    elevation: 2,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    color: theme.colors.error,
    fontSize: 12,
    marginTop: 4,
    marginLeft: 12,
  },
  segmentedButtons: {
    marginBottom: 8,
  },
  submitButton: {
    marginTop: 16,
  },
});
