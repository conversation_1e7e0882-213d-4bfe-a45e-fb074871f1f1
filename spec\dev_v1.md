# Relateful.app MVP v1 - Development Plan

## Overview
This document outlines the complete development plan for the Minimum Viable Product (MVP) of Relateful.app, focusing exclusively on **P0 (Priority 0)** features that are essential for launch. The app will be built using React Native with Expo for cross-platform deployment on iOS, Android, and Web.

## P0 Features Summary
Based on the feature specification, the MVP must include:

### Core App Experience & Onboarding
- **Onboarding Wizard**: Brief tutorial explaining Dunbar's Circles concept
- **Main Dashboard**: Central screen with reminders and activity summary
- **Contact Import & Creation**: Import from phonebook + manual creation
- **Interaction Logging**: Simple form to log interactions with notes

### Relationship Management (Dunbar's Circles)
- **<PERSON>'s Circles Implementation**: 4 pre-defined circles with contact assignment
- **Circle-Based Reminders**: Automatic scheduling based on circle membership
- **Contact Profile View**: Individual contact details with interaction history

### Guided Journeys
- **"Maintenance" Journey**: Core reminder and logging workflow

### Monetization
- **Free Tier**: Limited contacts in inner circles, basic functionality

## Technical Architecture

### Technology Stack
- **Framework**: React Native with Expo SDK 53
- **Platform Support**: iOS, Android, Web
- **State Management**: React Context + useReducer (for MVP simplicity)
- **Local Storage**: AsyncStorage for contact data and preferences
- **Navigation**: React Navigation v6
- **UI Components**: React Native Elements or NativeBase
- **Notifications**: Expo Notifications
- **Permissions**: Expo Contacts for phonebook access

### Project Structure
```
relateful/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # Screen components
│   ├── navigation/         # Navigation configuration
│   ├── context/           # Global state management
│   ├── services/          # Business logic and data services
│   ├── utils/             # Helper functions
│   └── constants/         # App constants and configurations
├── assets/                # Images, icons, fonts
└── App.js                # Root component
```

## Detailed Development Tasks

### Phase 1: Foundation & Setup (Week 1) ✅ COMPLETED
1. **Project Configuration**
   - [x] Configure Expo app.json for proper branding
   - [x] Set up navigation structure with React Navigation
   - [x] Configure AsyncStorage for data persistence
   - [x] Set up basic UI component library

2. **Core Data Models**
   - [x] Define Contact data structure
   - [x] Define Interaction data structure
   - [x] Define Dunbar Circle configurations
   - [x] Create data service layer for CRUD operations

### Phase 2: Contact Management (Week 2)
3. **Contact Import & Creation**
   - [ ] Implement contact import from device phonebook
   - [x] Create manual contact creation form
   - [x] Build contact list view with search functionality
   - [x] Implement contact editing capabilities

4. **Dunbar's Circles Implementation**
   - [ ] Create circle assignment interface
   - [ ] Implement circle-based contact organization
   - [ ] Build circle overview screens
   - [ ] Set up default reminder frequencies per circle

### Phase 3: Core Functionality (Week 3)
5. **Interaction Logging**
   - [ ] Create interaction logging form
   - [ ] Implement date picker for interaction dates
   - [ ] Add notes field with character limit
   - [ ] Store interaction history per contact

6. **Contact Profile View**
   - [ ] Build detailed contact profile screen
   - [ ] Display contact information and circle assignment
   - [ ] Show chronological interaction history
   - [ ] Add quick action buttons (call, message, log interaction)

### Phase 4: Reminders & Dashboard (Week 4)
7. **Circle-Based Reminders**
   - [ ] Implement reminder calculation logic
   - [ ] Set up local notification system
   - [ ] Create reminder management interface
   - [ ] Allow snoozing and marking reminders as complete

8. **Main Dashboard**
   - [ ] Build dashboard with "up next" reminders
   - [ ] Show recent activity summary
   - [ ] Add quick access to core features
   - [ ] Implement dashboard refresh functionality

### Phase 5: Onboarding & Polish (Week 5)
9. **Onboarding Wizard**
   - [ ] Create welcome screens explaining Dunbar's Circles
   - [ ] Guide users through adding first contacts
   - [ ] Implement skip functionality
   - [ ] Set up first-time user experience

10. **Free Tier Implementation**
    - [ ] Implement contact limits for free tier
    - [ ] Create upgrade prompts and messaging
    - [ ] Set up basic analytics tracking
    - [ ] Add settings screen for preferences

### Phase 6: Testing & Deployment (Week 6)
11. **Testing & Bug Fixes**
    - [ ] Comprehensive testing on all platforms
    - [ ] Performance optimization
    - [ ] UI/UX refinements
    - [ ] Bug fixes and edge case handling

12. **Deployment Preparation**
    - [ ] Configure app store metadata
    - [ ] Prepare app icons and screenshots
    - [ ] Set up analytics and crash reporting
    - [ ] Create privacy policy and terms of service

## Data Models

### Contact
```javascript
{
  id: string,
  name: string,
  phone?: string,
  email?: string,
  circle: 'support' | 'sympathy' | 'active' | 'full',
  lastInteraction?: Date,
  nextReminder?: Date,
  customFrequency?: number, // days
  notes?: string,
  createdAt: Date,
  updatedAt: Date
}
```

### Interaction
```javascript
{
  id: string,
  contactId: string,
  type: 'call' | 'text' | 'meeting' | 'other',
  date: Date,
  notes?: string,
  createdAt: Date
}
```

### Dunbar Circles Configuration
```javascript
{
  support: { limit: 5, frequency: 7 },      // Weekly
  sympathy: { limit: 15, frequency: 14 },   // Bi-weekly
  active: { limit: 50, frequency: 30 },     // Monthly
  full: { limit: 150, frequency: 90 }       // Quarterly
}
```

## Success Metrics for MVP
- [ ] Users can successfully import and organize contacts into circles
- [ ] Reminder system works reliably across all platforms
- [ ] Interaction logging is intuitive and quick
- [ ] App performs well on all target platforms
- [ ] Onboarding completion rate > 70%
- [ ] Daily active usage after first week > 40%

## Post-MVP Considerations
After successful MVP launch, the next phase will focus on P1 features:
- Customizable reminder frequencies
- Actionable notifications
- Connection streaks
- One-time Pro purchase
- Basic AI conversation starters

## Risk Mitigation
- **Platform-specific issues**: Test early and often on all platforms
- **Performance**: Keep data operations simple and efficient
- **User adoption**: Focus on clear onboarding and immediate value
- **Data privacy**: Implement proper data handling from the start
