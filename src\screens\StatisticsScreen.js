import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Appbar,
  Card,
  Text,
  Surface,
  IconButton,
  useTheme
} from 'react-native-paper';
import { useApp } from '../context/AppContext';
import CircleStatistics from '../components/CircleStatistics';
import InteractionAnalytics from '../components/InteractionAnalytics';

export default function StatisticsScreen() {
  const { state } = useApp();
  const theme = useTheme();

  // Create theme-aware styles
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title="Statistics" />
      </Appbar.Header>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.welcomeCard}>
          <Card.Content>
            <Text variant="headlineMedium" style={styles.title}>
              Your Relationship Analytics
            </Text>
            <Text variant="bodyLarge" style={styles.subtitle}>
              Insights into your connection patterns and circle health
            </Text>
          </Card.Content>
        </Card>
        
        <View style={styles.statsContainer}>
          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Surface style={styles.statIconContainer} elevation={1}>
                <IconButton
                  icon="account-group"
                  size={32}
                  iconColor={theme.colors.primary}
                />
              </Surface>
              <Text variant="displaySmall" style={styles.statNumber}>
                {state.contacts.length}
              </Text>
              <Text variant="bodyMedium" style={styles.statLabel}>
                Total Contacts
              </Text>
            </Card.Content>
          </Card>
          
          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Surface style={styles.statIconContainer} elevation={1}>
                <IconButton 
                  icon="chart-line" 
                  size={32}
                  iconColor="#45B7D1"
                />
              </Surface>
              <Text variant="displaySmall" style={styles.statNumber}>
                {state.interactions.length}
              </Text>
              <Text variant="bodyMedium" style={styles.statLabel}>
                Total Interactions
              </Text>
            </Card.Content>
          </Card>
        </View>
        
        <View style={styles.componentContainer}>
          <CircleStatistics contacts={state.contacts} />
        </View>
        
        <View style={styles.componentContainer}>
          <InteractionAnalytics 
            contacts={state.contacts} 
            interactions={state.interactions} 
          />
        </View>
        
        <Card style={styles.insightsCard}>
          <Card.Content>
            <View style={styles.insightsHeader}>
              <IconButton 
                icon="brain" 
                size={24}
                iconColor="#96CEB4"
              />
              <Text variant="titleMedium" style={styles.insightsTitle}>
                Understanding Your Data
              </Text>
            </View>
            <Text variant="bodyMedium" style={styles.insightsText}>
              • <Text style={styles.bold}>Circle Statistics</Text> show how your contacts are distributed across Dunbar's relationship circles{'\n'}
              • <Text style={styles.bold}>Maintenance Scores</Text> indicate how well you're staying connected with each circle{'\n'}
              • <Text style={styles.bold}>Communication Patterns</Text> reveal your preferred ways of staying in touch{'\n'}
              • <Text style={styles.bold}>Trends</Text> help you track your relationship-building progress over time
            </Text>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  welcomeCard: {
    marginBottom: 16,
    elevation: 2,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  subtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    elevation: 2,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  statIconContainer: {
    borderRadius: 50,
    marginBottom: 8,
  },
  statNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
    color: theme.colors.onSurface,
  },
  statLabel: {
    opacity: 0.7,
  },
  componentContainer: {
    marginBottom: 16,
  },
  insightsCard: {
    marginBottom: 16,
    elevation: 2,
  },
  insightsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  insightsTitle: {
    fontWeight: 'bold',
    marginLeft: 8,
  },
  insightsText: {
    lineHeight: 24,
    opacity: 0.8,
  },
  bold: {
    fontWeight: 'bold',
  },
});
