/**
 * Test Data Service
 * Handles loading sample data for development and testing
 */

import { Contact, Interaction, ReminderRule } from '../utils/dataModels';
import { DUNBAR_CIRCLES } from '../constants/dunbarCircles';

// Sample test data (embedded for React Native compatibility)
const TEST_DATA = [
  {
    name: "<PERSON>",
    phone: "******-0101",
    email: "<EMAIL>",
    circle: "support",
    categories: ["friend"],
    notes: "My best friend from college, always there for me",
    lastInteracted: "2024-12-20",
    interactionType: "call",
    interactionQuality: "deep",
    interactionMood: "positive",
    interactionNotes: "Had a great heart-to-heart about life goals"
  },
  {
    name: "<PERSON>",
    phone: "******-0102",
    email: "<EMAIL>",
    circle: "support",
    categories: ["friend"],
    notes: "Brother-in-law, family gatherings",
    lastInteracted: "2024-12-18",
    interactionType: "meeting",
    interactionQuality: "good",
    interactionMood: "positive",
    interactionNotes: "Family dinner at mom's house"
  },
  {
    name: "<PERSON>",
    phone: "******-0103",
    email: "<EMAIL>",
    circle: "sympathy",
    categories: ["friend"],
    notes: "Close friend from work, lunch buddy",
    lastInteracted: "2024-12-15",
    interactionType: "meeting",
    interactionQuality: "good",
    interactionMood: "positive",
    interactionNotes: "Lunch at our favorite cafe downtown"
  },
  {
    name: "David Wilson",
    phone: "******-0104",
    email: "<EMAIL>",
    circle: "sympathy",
    categories: ["coworker"],
    notes: "Team lead, great mentor",
    lastInteracted: "2024-12-10",
    interactionType: "call",
    interactionQuality: "good",
    interactionMood: "positive",
    interactionNotes: "Weekly team check-in call"
  },
  {
    name: "Emma Brown",
    phone: "******-0105",
    email: "<EMAIL>",
    circle: "active",
    categories: ["friend"],
    notes: "Yoga class friend, weekend hikes",
    lastInteracted: "2024-12-05",
    interactionType: "text",
    interactionQuality: "brief",
    interactionMood: "positive",
    interactionNotes: "Quick text about weekend hiking plans"
  },
  {
    name: "Frank Miller",
    phone: "******-0106",
    email: "<EMAIL>",
    circle: "active",
    categories: ["hobby"],
    notes: "Guitar playing buddy, jam sessions",
    lastInteracted: "2024-11-28",
    interactionType: "meeting",
    interactionQuality: "deep",
    interactionMood: "positive",
    interactionNotes: "Amazing jam session at his place"
  },
  {
    name: "Grace Taylor",
    phone: "******-0107",
    email: "<EMAIL>",
    circle: "active",
    categories: ["coworker"],
    notes: "Marketing colleague, project partner",
    lastInteracted: "2024-11-25",
    interactionType: "call",
    interactionQuality: "good",
    interactionMood: "positive",
    interactionNotes: "Project planning call for Q1 campaign"
  },
  {
    name: "Henry Anderson",
    phone: "******-0108",
    email: "<EMAIL>",
    circle: "full",
    categories: ["acquaintance"],
    notes: "Neighbor, occasional chats",
    lastInteracted: "2024-11-15",
    interactionType: "meeting",
    interactionQuality: "brief",
    interactionMood: "positive",
    interactionNotes: "Quick chat while walking the dog"
  },
  {
    name: "Ivy Thompson",
    phone: "******-0109",
    email: "<EMAIL>",
    circle: "full",
    categories: ["hobby"],
    notes: "Book club member, monthly meetings",
    lastInteracted: "2024-10-30",
    interactionType: "meeting",
    interactionQuality: "good",
    interactionMood: "positive",
    interactionNotes: "Book club discussion about latest novel"
  },
  {
    name: "Jack Martinez",
    phone: "******-0110",
    email: "<EMAIL>",
    circle: "full",
    categories: ["acquaintance"],
    notes: "Coffee shop regular, friendly conversations",
    lastInteracted: "2024-10-20",
    interactionType: "meeting",
    interactionQuality: "brief",
    interactionMood: "positive",
    interactionNotes: "Brief chat at the coffee shop"
  }
];

/**
 * Generate test contacts and interactions from sample data
 * @returns {Object} Object containing contacts and interactions arrays
 */
export const generateTestData = () => {
  const contacts = [];
  const interactions = [];
  const now = new Date();

  TEST_DATA.forEach((data, index) => {
    // Create contact
    const circle = DUNBAR_CIRCLES[data.circle];
    const reminderRule = new ReminderRule({
      frequency: circle.frequency,
      createdDate: now
    });

    const contact = new Contact({
      name: data.name,
      phone: data.phone,
      email: data.email,
      circle: data.circle,
      categories: data.categories,
      notes: data.notes,
      lastInteracted: new Date(data.lastInteracted),
      recurringReminder: reminderRule,
      createdAt: new Date(now.getTime() - (index * 24 * 60 * 60 * 1000)), // Stagger creation dates
      updatedAt: new Date(data.lastInteracted)
    });

    contacts.push(contact);

    // Create interaction
    const interaction = new Interaction({
      contactId: contact.id,
      type: data.interactionType,
      quality: data.interactionQuality,
      mood: data.interactionMood,
      notes: data.interactionNotes,
      date: new Date(data.lastInteracted),
      createdAt: new Date(data.lastInteracted)
    });

    interactions.push(interaction);
  });

  return { contacts, interactions };
};

/**
 * Check if we're in development mode
 * @returns {boolean} True if in development mode
 */
export const isDevelopmentMode = () => {
  return __DEV__;
};

export default {
  generateTestData,
  isDevelopmentMode
};
