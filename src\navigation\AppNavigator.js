import React from 'react';
import { Platform } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Screens
import RelateScreen from '../screens/RelateScreen';
import LogScreen from '../screens/LogScreen';
import StatisticsScreen from '../screens/StatisticsScreen';
import ContactsScreen from '../screens/ContactsScreen';
import ContactDetailScreen from '../screens/ContactDetailScreen';
import AddContactScreen from '../screens/AddContactScreen';
import EditContactScreen from '../screens/EditContactScreen';
import LogInteractionScreen from '../screens/LogInteractionScreen';
import LogDetailScreen from '../screens/LogDetailScreen';
import EditInteractionScreen from '../screens/EditInteractionScreen';
import SettingsScreen from '../screens/SettingsScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Relate Stack Navigator
function RelateStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="RelateHome" component={RelateScreen} />
      <Stack.Screen name="LogInteraction" component={LogInteractionScreen} />
    </Stack.Navigator>
  );
}

// Log Stack Navigator
function LogStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="LogHome" component={LogScreen} />
      <Stack.Screen name="LogDetail" component={LogDetailScreen} />
      <Stack.Screen name="EditInteraction" component={EditInteractionScreen} />
    </Stack.Navigator>
  );
}

// Contacts Stack Navigator
function ContactsStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="ContactsList" component={ContactsScreen} />
      <Stack.Screen name="AddContact" component={AddContactScreen} />
      <Stack.Screen name="ContactDetail" component={ContactDetailScreen} />
      <Stack.Screen name="EditContact" component={EditContactScreen} />
      <Stack.Screen name="LogInteraction" component={LogInteractionScreen} />
      <Stack.Screen name="LogDetail" component={LogDetailScreen} />
      <Stack.Screen name="EditInteraction" component={EditInteractionScreen} />
    </Stack.Navigator>
  );
}

export default function AppNavigator({ navigationTheme }) {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  return (
    <NavigationContainer theme={navigationTheme}>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName;

            if (route.name === 'Relate') {
              iconName = focused ? 'heart' : 'heart-outline';
            } else if (route.name === 'Log') {
              iconName = focused ? 'list' : 'list-outline';
            } else if (route.name === 'Statistics') {
              iconName = focused ? 'analytics' : 'analytics-outline';
            } else if (route.name === 'Contacts') {
              iconName = focused ? 'people' : 'people-outline';
            } else if (route.name === 'Settings') {
              iconName = focused ? 'settings' : 'settings-outline';
            }

            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: theme.colors.primary,
          tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
          tabBarStyle: {
            backgroundColor: theme.colors.surface,
            borderTopWidth: 1,
            borderTopColor: theme.colors.outline,
            paddingBottom: Math.max(insets.bottom, 5),
            paddingTop: 5,
            height: 60 + Math.max(insets.bottom - 5, 0),
          },
          tabBarLabelStyle: {
            fontSize: 12,
            fontWeight: '500',
          },
          headerShown: false, // We'll use custom headers in each screen
        })}
      >
        <Tab.Screen
          name="Relate"
          component={RelateStack}
          options={{
            tabBarLabel: 'Relate',
          }}
        />
        <Tab.Screen
          name="Log"
          component={LogStack}
          options={{
            tabBarLabel: 'Log',
          }}
        />
        <Tab.Screen
          name="Statistics"
          component={StatisticsScreen}
          options={{
            tabBarLabel: 'Statistics',
          }}
        />
        <Tab.Screen
          name="Contacts"
          component={ContactsStack}
          options={{
            tabBarLabel: 'Contacts',
          }}
        />
        <Tab.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            tabBarLabel: 'Settings',
          }}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
}
