import { DUNBAR_CIRCLES, CIRCLE_TYPES, INTERACTION_TYPES } from '../constants/dunbarCircles';

/**
 * Interaction Analytics Engine
 * Analyzes communication patterns and provides insights
 */

// Helper function to get days between dates
const getDaysBetween = (date1, date2) => {
  const diffTime = Math.abs(date2 - date1);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Helper function to get start of period
const getStartOfPeriod = (period = 'month') => {
  const now = new Date();
  switch (period) {
    case 'week':
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      startOfWeek.setHours(0, 0, 0, 0);
      return startOfWeek;
    case 'month':
      return new Date(now.getFullYear(), now.getMonth(), 1);
    case 'quarter':
      const quarter = Math.floor(now.getMonth() / 3);
      return new Date(now.getFullYear(), quarter * 3, 1);
    default:
      return new Date(now.getFullYear(), now.getMonth(), 1);
  }
};

/**
 * Analyze interaction patterns for all contacts
 */
export const analyzeInteractionPatterns = (contacts, interactions) => {
  const now = new Date();
  const startOfMonth = getStartOfPeriod('month');
  const startOfWeek = getStartOfPeriod('week');

  // Filter recent interactions
  const recentInteractions = interactions.filter(i => new Date(i.date) >= startOfMonth);
  const weeklyInteractions = interactions.filter(i => new Date(i.date) >= startOfWeek);

  // Calculate basic metrics
  const totalInteractions = interactions.length;
  const monthlyInteractions = recentInteractions.length;
  const weeklyInteractionCount = weeklyInteractions.length;

  // Analyze by circle
  const circleAnalysis = analyzeByCircle(contacts, interactions);

  // Analyze interaction types
  const typeAnalysis = analyzeInteractionTypes(interactions);

  // Analyze frequency patterns
  const frequencyAnalysis = analyzeFrequencyPatterns(contacts, interactions);

  // Analyze trends
  const trendAnalysis = analyzeTrends(interactions);

  // Generate insights
  const insights = generateInsights(contacts, interactions, {
    circleAnalysis,
    typeAnalysis,
    frequencyAnalysis,
    trendAnalysis
  });

  return {
    summary: {
      totalContacts: contacts.length,
      totalInteractions,
      monthlyInteractions,
      weeklyInteractions: weeklyInteractionCount,
      averageInteractionsPerWeek: weeklyInteractionCount,
      averageInteractionsPerMonth: monthlyInteractions
    },
    circleAnalysis,
    typeAnalysis,
    frequencyAnalysis,
    trendAnalysis,
    insights
  };
};

/**
 * Analyze interactions by Dunbar circle
 */
const analyzeByCircle = (contacts, interactions) => {
  const analysis = {};

  CIRCLE_TYPES.forEach(circleKey => {
    const circle = DUNBAR_CIRCLES[circleKey];
    const circleContacts = contacts.filter(c => c.circle === circleKey);
    const circleInteractions = interactions.filter(i => 
      circleContacts.some(c => c.id === i.contactId)
    );

    const now = new Date();
    const recentInteractions = circleInteractions.filter(i => 
      getDaysBetween(new Date(i.date), now) <= 30
    );

    analysis[circleKey] = {
      ...circle,
      contactCount: circleContacts.length,
      totalInteractions: circleInteractions.length,
      monthlyInteractions: recentInteractions.length,
      averageFrequency: circleContacts.length > 0 
        ? recentInteractions.length / circleContacts.length 
        : 0,
      recommendedFrequency: 30 / circle.frequency, // interactions per month
      maintenanceScore: calculateMaintenanceScore(circleContacts, circleInteractions, circle.frequency)
    };
  });

  return analysis;
};

/**
 * Analyze interaction types and preferences
 */
const analyzeInteractionTypes = (interactions) => {
  const typeCounts = {};
  const recentInteractions = interactions.filter(i => 
    getDaysBetween(new Date(i.date), new Date()) <= 30
  );

  // Count by type
  Object.keys(INTERACTION_TYPES).forEach(type => {
    typeCounts[type] = {
      ...INTERACTION_TYPES[type],
      count: interactions.filter(i => i.type === type).length,
      recentCount: recentInteractions.filter(i => i.type === type).length,
      percentage: 0
    };
  });

  // Calculate percentages
  const total = interactions.length;
  Object.keys(typeCounts).forEach(type => {
    typeCounts[type].percentage = total > 0 ? (typeCounts[type].count / total) * 100 : 0;
  });

  // Find preferred type
  const preferredType = Object.keys(typeCounts).reduce((a, b) => 
    typeCounts[a].count > typeCounts[b].count ? a : b
  );

  return {
    typeCounts,
    preferredType,
    totalInteractions: total,
    recentTotal: recentInteractions.length
  };
};

/**
 * Analyze frequency patterns
 */
const analyzeFrequencyPatterns = (contacts, interactions) => {
  const contactFrequencies = contacts.map(contact => {
    const contactInteractions = interactions.filter(i => i.contactId === contact.id);
    const circle = DUNBAR_CIRCLES[contact.circle];
    
    if (contactInteractions.length < 2) {
      return {
        contactId: contact.id,
        contactName: contact.name,
        circle: contact.circle,
        actualFrequency: null,
        recommendedFrequency: circle.frequency,
        isOnTrack: contactInteractions.length > 0
      };
    }

    // Calculate average days between interactions
    const sortedInteractions = contactInteractions
      .sort((a, b) => new Date(a.date) - new Date(b.date));
    
    let totalDays = 0;
    for (let i = 1; i < sortedInteractions.length; i++) {
      totalDays += getDaysBetween(
        new Date(sortedInteractions[i-1].date),
        new Date(sortedInteractions[i].date)
      );
    }
    
    const actualFrequency = totalDays / (sortedInteractions.length - 1);
    const isOnTrack = actualFrequency <= circle.frequency * 1.5; // 50% tolerance

    return {
      contactId: contact.id,
      contactName: contact.name,
      circle: contact.circle,
      actualFrequency,
      recommendedFrequency: circle.frequency,
      isOnTrack
    };
  });

  return {
    contactFrequencies,
    overallOnTrackPercentage: contactFrequencies.filter(cf => cf.isOnTrack).length / contacts.length * 100
  };
};

/**
 * Analyze trends over time
 */
const analyzeTrends = (interactions) => {
  const now = new Date();
  const periods = [];

  // Generate last 6 months of data
  for (let i = 5; i >= 0; i--) {
    const periodStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const periodEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
    
    const periodInteractions = interactions.filter(interaction => {
      const date = new Date(interaction.date);
      return date >= periodStart && date <= periodEnd;
    });

    periods.push({
      month: periodStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      count: periodInteractions.length,
      date: periodStart
    });
  }

  // Calculate trend direction
  const recentAverage = periods.slice(-3).reduce((sum, p) => sum + p.count, 0) / 3;
  const earlierAverage = periods.slice(0, 3).reduce((sum, p) => sum + p.count, 0) / 3;
  const trendDirection = recentAverage > earlierAverage ? 'improving' : 
                        recentAverage < earlierAverage ? 'declining' : 'stable';

  return {
    monthlyData: periods,
    trendDirection,
    recentAverage,
    earlierAverage
  };
};

/**
 * Calculate maintenance score for a circle
 */
const calculateMaintenanceScore = (contacts, interactions, recommendedFrequency) => {
  if (contacts.length === 0) return 100;

  const now = new Date();
  let totalScore = 0;

  contacts.forEach(contact => {
    const contactInteractions = interactions.filter(i => i.contactId === contact.id);
    
    if (contactInteractions.length === 0) {
      totalScore += 0; // No interactions = 0 score
      return;
    }

    // Find most recent interaction
    const lastInteraction = contactInteractions.reduce((latest, current) => 
      new Date(current.date) > new Date(latest.date) ? current : latest
    );

    const daysSinceLastContact = getDaysBetween(new Date(lastInteraction.date), now);
    const score = Math.max(0, 100 - (daysSinceLastContact / recommendedFrequency) * 100);
    totalScore += score;
  });

  return Math.round(totalScore / contacts.length);
};

/**
 * Generate actionable insights
 */
const generateInsights = (contacts, interactions, analysis) => {
  const insights = [];

  // Circle maintenance insights
  Object.keys(analysis.circleAnalysis).forEach(circleKey => {
    const circle = analysis.circleAnalysis[circleKey];
    
    if (circle.maintenanceScore < 70 && circle.contactCount > 0) {
      insights.push({
        type: 'circle_maintenance',
        priority: 'medium',
        title: `${circle.name} needs attention`,
        message: `Your ${circle.name.toLowerCase()} could use more regular contact`,
        suggestion: `Try reaching out to someone in this circle this week`,
        circleKey
      });
    }
  });

  // Frequency insights
  if (analysis.frequencyAnalysis.overallOnTrackPercentage > 80) {
    insights.push({
      type: 'positive',
      priority: 'low',
      title: 'Great job staying connected!',
      message: `You're maintaining ${Math.round(analysis.frequencyAnalysis.overallOnTrackPercentage)}% of your relationships well`,
      suggestion: 'Keep up the excellent work!'
    });
  }

  // Trend insights
  if (analysis.trendAnalysis.trendDirection === 'improving') {
    insights.push({
      type: 'positive',
      priority: 'low',
      title: 'Your connections are growing stronger',
      message: 'You\'ve been more active in reaching out lately',
      suggestion: 'This positive trend is great for your relationships!'
    });
  }

  return insights;
};
