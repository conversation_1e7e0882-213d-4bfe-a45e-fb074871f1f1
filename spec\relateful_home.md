# Relateful Home (called Relate)
This is the entry point of the app and consists of the following sections:

## Upcoming Reminders
Reminders of contacts that are overdue or coming up in the next 5 days (configurable in `src/constants/reminderConfig.js`). Each contact should have:
- The name of the contact (as a heading)
- The circle the contact belongs to next to a list of tags the contact belongs to
- Two buttons at the bottom:
  - Snooze (clock icon) which will open the "Snooze popup".
  - Log (pen icon) which will open the log interaction screen for that contact.
  After completing the Snooze or Log action, will disappear from the list, but it should do so with a nice effect.

### Snooze Popup
When the snooze button is pressed, a popup should appear with the following options:
- Snooze for 1 day
- Snooze for 1 week
- Snooze for 1 month
- Snooze for 1 year
- Snooze indefinitely

### Log interaction screen
When the log button is pressed, a screen should appear with the following options:
- A text input for the user to write a note about the interaction
- A date picker to select the date of the interaction
- A button to save the interaction

## Relationship Overview
Give basic stats for your communication patterns:
- Number of contacts
- Number of tracked interactions in the last week
- Number of tracked interactions in the last month
- A small trend how good you are doing with maintaining the contacts across all circles (on track score)

## Circle Maintenance
Lists all circles. Each circle is represented with a name, a health bar indicating your contact complicance score (0 - 100%). Also shows the number of interactions in the last month.

