import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Appbar,
  Text,
  Card,
  Surface,
  IconButton,
  Avatar,
  FAB,
  useTheme,
  TouchableRipple
} from 'react-native-paper';
import { useApp } from '../context/AppContext';
import { INTERACTION_TYPES, DUNBAR_CIRCLES } from '../constants/dunbarCircles';

export default function LogDetailScreen({ route, navigation }) {
  const { interaction: routeInteraction } = route.params;
  const { state } = useApp();
  const theme = useTheme();
  const [contact, setContact] = useState(null);

  // Create theme-aware styles
  const styles = createStyles(theme);

  // Get the current interaction data from state (in case it was updated)
  const interaction = state.interactions.find(i => i.id === routeInteraction.id) || routeInteraction;

  useEffect(() => {
    // Find the contact associated with this interaction
    const associatedContact = state.contacts.find(c => c.id === interaction.contactId);
    setContact(associatedContact);
  }, [state.contacts, interaction.contactId]);

  // Get interaction type information
  const interactionType = INTERACTION_TYPES[interaction.type];
  const interactionDate = new Date(interaction.date);
  const createdDate = new Date(interaction.createdAt);

  // Format relative date
  const getRelativeDate = () => {
    const now = new Date();
    const daysDiff = Math.ceil((now - interactionDate) / (1000 * 60 * 60 * 24));

    if (daysDiff === 0) return 'Today';
    if (daysDiff === 1) return 'Yesterday';
    if (daysDiff < 7) return `${daysDiff} days ago`;
    if (daysDiff < 30) {
      const weeks = Math.floor(daysDiff / 7);
      return `${weeks} week${weeks === 1 ? '' : 's'} ago`;
    }
    if (daysDiff < 365) {
      const months = Math.floor(daysDiff / 30);
      return `${months} month${months === 1 ? '' : 's'} ago`;
    }
    const years = Math.floor(daysDiff / 365);
    return `${years} year${years === 1 ? '' : 's'} ago`;
  };

  // Get mood color
  const getMoodColor = () => {
    switch (interaction.mood) {
      case 'positive': return '#4CAF50';
      case 'neutral': return '#FF9800';
      case 'challenging': return '#FF6B6B';
      default: return '#9E9E9E';
    }
  };

  // Get quality text and color
  const getQualityInfo = () => {
    switch (interaction.quality) {
      case 'brief': return { text: 'Brief', color: '#9E9E9E' };
      case 'good': return { text: 'Good', color: '#4CAF50' };
      case 'deep': return { text: 'Deep', color: '#2196F3' };
      default: return { text: 'Good', color: '#4CAF50' };
    }
  };

  // Format duration
  const formatDuration = (minutes) => {
    if (!minutes) return null;
    if (minutes < 60) return `${minutes} minutes`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) return `${hours} hour${hours === 1 ? '' : 's'}`;
    return `${hours} hour${hours === 1 ? '' : 's'} ${remainingMinutes} minutes`;
  };

  // Handle contact navigation
  const handleContactPress = () => {
    if (contact) {
      navigation.navigate('Contacts', {
        screen: 'ContactDetail',
        params: { contact: contact.toJSON ? contact.toJSON() : contact }
      });
    }
  };

  // Handle edit interaction
  const handleEditInteraction = () => {
    navigation.navigate('EditInteraction', { 
      interaction,
      contact: contact || { id: interaction.contactId, name: 'Unknown Contact' }
    });
  };

  const circle = contact ? DUNBAR_CIRCLES[contact.circle] : null;
  const qualityInfo = getQualityInfo();

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="Interaction Detail" />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Contact Information Section */}
        <Card style={styles.card}>
          <TouchableRipple onPress={handleContactPress} disabled={!contact}>
            <Card.Content>
              <View style={styles.contactHeader}>
                <Avatar.Text
                  size={56}
                  label={contact?.name?.charAt(0).toUpperCase() || '?'}
                  style={[styles.avatar, { backgroundColor: circle?.color || '#9E9E9E' }]}
                />
                <View style={styles.contactInfo}>
                  <Text variant="titleLarge" style={styles.contactName}>
                    {contact?.name || 'Unknown Contact'}
                  </Text>
                  {circle && (
                    <View style={styles.circleContainer}>
                      <View style={[styles.circleIndicator, { backgroundColor: circle.color }]} />
                      <Text variant="bodyMedium" style={styles.circleName}>
                        {circle.name}
                      </Text>
                    </View>
                  )}
                </View>
                {contact && <IconButton icon="chevron-right" size={20} />}
              </View>
            </Card.Content>
          </TouchableRipple>
        </Card>

        {/* Interaction Overview Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Interaction Overview
            </Text>
            
            <View style={styles.interactionHeader}>
              <View style={styles.typeContainer}>
                <Surface 
                  style={[styles.typeIconContainer, { backgroundColor: interactionType.color + '20' }]} 
                  elevation={1}
                >
                  <IconButton 
                    icon={interactionType.icon} 
                    size={24}
                    iconColor={interactionType.color}
                    style={styles.typeIcon}
                  />
                </Surface>
                <View style={styles.typeInfo}>
                  <Text variant="titleMedium" style={styles.typeName}>
                    {interactionType.name}
                  </Text>
                  <Text variant="bodyMedium" style={styles.dateText}>
                    {getRelativeDate()} • {interactionDate.toLocaleDateString()}
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.metadataContainer}>
              {interaction.duration && (
                <View style={styles.metadataItem}>
                  <Text variant="bodySmall" style={styles.metadataLabel}>Duration</Text>
                  <Text variant="bodyMedium" style={styles.metadataValue}>
                    {formatDuration(interaction.duration)}
                  </Text>
                </View>
              )}
              
              <View style={styles.metadataItem}>
                <Text variant="bodySmall" style={styles.metadataLabel}>Quality</Text>
                <View style={styles.qualityContainer}>
                  <View style={[styles.qualityIndicator, { backgroundColor: qualityInfo.color }]} />
                  <Text variant="bodyMedium" style={[styles.metadataValue, { color: qualityInfo.color }]}>
                    {qualityInfo.text}
                  </Text>
                </View>
              </View>

              <View style={styles.metadataItem}>
                <Text variant="bodySmall" style={styles.metadataLabel}>Mood</Text>
                <View style={styles.moodContainer}>
                  <View style={[styles.moodIndicator, { backgroundColor: getMoodColor() }]} />
                  <Text variant="bodyMedium" style={styles.metadataValue}>
                    {interaction.mood.charAt(0).toUpperCase() + interaction.mood.slice(1)}
                  </Text>
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Interaction Details Section */}
        {interaction.notes && (
          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Notes
              </Text>
              <Text variant="bodyLarge" style={styles.notesText}>
                {interaction.notes}
              </Text>
            </Card.Content>
          </Card>
        )}

        {/* Metadata Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Details
            </Text>
            <View style={styles.metadataList}>
              <View style={styles.metadataRow}>
                <Text variant="bodySmall" style={styles.metadataLabel}>Logged on</Text>
                <Text variant="bodyMedium" style={styles.metadataValue}>
                  {createdDate.toLocaleDateString()} at {createdDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Floating Action Button for Edit */}
      <FAB
        icon="pencil"
        style={styles.fab}
        onPress={handleEditInteraction}
      />
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    marginRight: 16,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  circleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  circleIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  circleName: {
    fontWeight: '500',
  },
  interactionHeader: {
    marginBottom: 16,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeIconContainer: {
    borderRadius: 20,
    marginRight: 16,
  },
  typeIcon: {
    margin: 0,
  },
  typeInfo: {
    flex: 1,
  },
  typeName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  dateText: {
    opacity: 0.7,
  },
  metadataContainer: {
    gap: 12,
  },
  metadataItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metadataLabel: {
    opacity: 0.7,
    fontWeight: '500',
  },
  metadataValue: {
    fontWeight: '500',
  },
  qualityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  qualityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  moodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  moodIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  notesText: {
    lineHeight: 22,
  },
  metadataList: {
    gap: 8,
  },
  metadataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});
