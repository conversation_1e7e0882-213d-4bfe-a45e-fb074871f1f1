import React, { useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import { 
  Card, 
  Text, 
  ProgressBar, 
  Surface,
  useTheme 
} from 'react-native-paper';
import { DUNBAR_CIRCLES, CIRCLE_TYPES } from '../constants/dunbarCircles';

export default function CircleStatistics({ contacts }) {
  const theme = useTheme();

  // Get health message with softer language
  const getHealthMessage = (stats) => {
    if (stats.count === 0) return 'Ready to add contacts';
    if (stats.count > stats.limit) {
      const extra = stats.count - stats.limit;
      if (extra === 1) return `${stats.count} contacts (1 more than typical)`;
      return `${stats.count} contacts (${extra} more than typical)`;
    }
    return `${stats.count} of ${stats.limit} contacts`;
  };

  // Calculate circle statistics
  const circleStats = useMemo(() => {
    const stats = {};

    CIRCLE_TYPES.forEach(circleKey => {
      const circle = DUNBAR_CIRCLES[circleKey];
      const contactsInCircle = contacts.filter(contact => contact.circle === circleKey);
      const count = contactsInCircle.length;
      const limit = circle.limit;
      const percentage = limit > 0 ? (count / limit) * 100 : 0;

      stats[circleKey] = {
        ...circle,
        count,
        limit,
        percentage: Math.min(percentage, 100),
        isOverLimit: count > limit
      };
    });

    return stats;
  }, [contacts]);



  const renderCircleCard = (circleKey) => {
    const stats = circleStats[circleKey];

    return (
      <Card key={circleKey} style={styles.circleCard}>
        <Card.Content style={styles.cardContent}>
          <View style={styles.circleHeader}>
            <View style={[styles.circleIndicator, { backgroundColor: stats.color }]} />
            <View style={styles.circleInfo}>
              <Text variant="titleMedium" style={styles.circleName}>
                {stats.name}
              </Text>
              <Text variant="bodySmall" style={styles.circleDescription}>
                {stats.description}
              </Text>
            </View>
          </View>

          <View style={styles.statsContainer}>
            <View style={styles.countContainer}>
              <Text variant="headlineSmall" style={[styles.count, { color: stats.color }]}>
                {stats.count}
              </Text>
              <Text variant="bodySmall" style={styles.countLabel}>
                contacts
              </Text>
            </View>

            <View style={styles.progressContainer}>
              <ProgressBar
                progress={stats.percentage / 100}
                color={stats.color}
                style={styles.progressBar}
              />
              <Text variant="bodySmall" style={styles.healthMessage}>
                {getHealthMessage(stats)}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    );
  };

  // Calculate total contacts and overall stats
  const totalContacts = contacts.length;
  const totalCapacity = CIRCLE_TYPES.reduce((sum, key) => sum + DUNBAR_CIRCLES[key].limit, 0);
  const expandedCircles = CIRCLE_TYPES.filter(key => circleStats[key].isOverLimit).length;

  return (
    <View style={styles.container}>
      <Card style={styles.summaryCard}>
        <Card.Content>
          <Text variant="titleLarge" style={styles.title}>
            Circle Overview
          </Text>
          
          <View style={styles.summaryStats}>
            <Surface style={styles.summaryItem} elevation={1}>
              <Text variant="headlineMedium" style={styles.summaryNumber}>
                {totalContacts}
              </Text>
              <Text variant="bodyMedium" style={styles.summaryLabel}>
                Total Contacts
              </Text>
            </Surface>
            
            <Surface style={styles.summaryItem} elevation={1}>
              <Text variant="headlineMedium" style={styles.summaryNumber}>
                {totalCapacity}
              </Text>
              <Text variant="bodyMedium" style={styles.summaryLabel}>
                Total Capacity
              </Text>
            </Surface>
            
            {expandedCircles > 0 && (
              <Surface style={[styles.summaryItem, styles.expandedItem]} elevation={1}>
                <Text variant="headlineMedium" style={[styles.summaryNumber, styles.expandedText]}>
                  {expandedCircles}
                </Text>
                <Text variant="bodyMedium" style={[styles.summaryLabel, styles.expandedText]}>
                  Expanded
                </Text>
              </Surface>
            )}
          </View>
        </Card.Content>
      </Card>

      <View style={styles.circlesContainer}>
        {CIRCLE_TYPES.map(renderCircleCard)}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    // Remove padding to match Dashboard card width
  },
  summaryCard: {
    marginBottom: 16,
    elevation: 2,
  },
  title: {
    marginBottom: 16,
    fontWeight: 'bold',
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  summaryItem: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    borderRadius: 8,
  },
  summaryNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summaryLabel: {
    opacity: 0.7,
    textAlign: 'center',
  },
  expandedItem: {
    backgroundColor: '#E3F2FD',
  },
  expandedText: {
    color: '#1976D2',
  },
  circlesContainer: {
    gap: 12,
  },
  circleCard: {
    elevation: 2,
  },
  cardContent: {
    padding: 16,
  },
  circleHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  circleIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
    marginTop: 4,
  },
  circleInfo: {
    flex: 1,
  },
  circleName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  circleDescription: {
    opacity: 0.7,
    lineHeight: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  countContainer: {
    alignItems: 'center',
    minWidth: 60,
  },
  count: {
    fontWeight: 'bold',
    marginBottom: 2,
  },
  countLabel: {
    opacity: 0.7,
  },
  progressContainer: {
    flex: 1,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  healthMessage: {
    fontWeight: '500',
    opacity: 0.8,
  },
});
