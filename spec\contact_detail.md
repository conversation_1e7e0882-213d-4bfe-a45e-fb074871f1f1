## Contact Detail
On Contact Detail show an overview of this contact, including:
- Name
- Dunbar Circle (with color indicator)
- Contact Categories/Tags (as colored chips)
- Contact Information (phone, email if available)
- Notes (if available)
- Next Reminder (with status and frequency information)
- List of last interactions (chronological order, most recent first)

### Contact Overview Section
The contact overview should display:
- **Avatar**: Colored circle with contact's first initial (using circle color)
- **Name**: Contact's full name prominently displayed
- **Circle**: Dunbar circle name with color indicator
- **Categories**: Contact categories displayed as colored chips
- **Contact Info**: Phone and email (if provided)
- **Notes**: Any notes about the contact (if available)

### Contact Reminder Fields
Each contact maintains three key fields for reminder tracking:

- **lastInteracted**: For which date the latest (not last) interaction was logged with this contact
- **nextReminderScheduled**: For which date the next reminder is scheduled to appear
- **lastReminderCompleted**: For which date the last reminder was marked as completed

These fields work together to provide accurate reminder scheduling and tracking throughout the contact detail interface.

#### Field Descriptions:
- **lastInteracted**: Stores the date of the most recent interaction that was logged for this contact. This is updated whenever a new interaction is submitted through the Log Interaction screen.
- **nextReminderScheduled**: Calculated field that determines when the next reminder should appear for this contact, based on the contact's frequency settings and last interaction/reminder completion dates.
- **lastReminderCompleted**: Tracks when a reminder was last marked as completed (either by clicking "Log Interaction" from the homepage or by other completion methods). This helps determine the next reminder schedule.

### Next Reminder Section
Display intelligent reminder status:
- **One-off Reminders**: Specific reminder dates set by user
- **Custom Frequencies**: Weekly, Monthly, Quarterly, Yearly
- **Circle-based Defaults**: Based on Dunbar circle recommendations
- **Status Indicators**: Overdue (red), due soon (orange), up to date (green)
- **Frequency Information**: Show current contact frequency setting

### Interaction History Section
Show chronological list of interactions:
- **Section Header**: "Interaction History" with a "Log Interaction" button aligned to the right
- **Interaction Type**: With appropriate icons and colors
- **Date**: Relative date (e.g., "2 days ago") and absolute date
- **Duration**: For calls and meetings
- **Quality & Mood**: Visual indicators
- **Notes**: Any notes from the interaction
- **Log Interaction Button**: Material Design outlined button in section header for easy access
- **Empty State**: Encouraging message when no interactions exist, with prominent "Log Interaction" button to get started

### Material Design Elements
- **Floating Action Button**: Edit button in bottom right corner
- **Log Interaction Button**: Outlined button in Interaction History section header, right-aligned
- **Card Layout**: Each section in Material Design cards
- **Color Coding**: Consistent with Dunbar circles and interaction types
- **Typography**: Material Design text hierarchy
- **Spacing**: Proper margins and padding for readability

### Interaction Logging
- **Button Placement**: "Log Interaction" button in Interaction History section header
- **Button Style**: Material Design outlined button with appropriate icon
- **Empty State Integration**: When no interactions exist, the empty state message should include the "Log Interaction" button as a call-to-action
- **Navigation**: Button navigates to Log Interaction screen for this specific contact

### Navigation Flow
- **Entry**: Accessible by tapping contact from Contacts screen
- **Edit**: FAB navigates to Edit Contact screen
- **Log Interaction**: Button in Interaction History section navigates to Log Interaction screen
- **Return**: Proper back navigation to Contacts list
- **Data Sync**: Updates reflect when returning from edit or log interaction screens

In typical material fashion there is a edit button in the bottom right moving along. This edit button should take you to the "Edit Contact" screen explained in edit_contact.md