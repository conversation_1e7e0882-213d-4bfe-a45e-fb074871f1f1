import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Appbar,
  Card,
  Text,
  Surface,
  IconButton
} from 'react-native-paper';
import { useApp } from '../context/AppContext';
import CircleStatistics from '../components/CircleStatistics';
import InteractionAnalytics from '../components/InteractionAnalytics';

export default function DashboardScreen() {
  const { state, actions } = useApp();

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title="Dashboard" />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.welcomeCard}>
          <Card.Content>
            <Text variant="headlineMedium" style={styles.title}>
              Welcome to Relateful
            </Text>
            <Text variant="bodyLarge" style={styles.subtitle}>
              Your relationship management dashboard
            </Text>
          </Card.Content>
        </Card>

        <View style={styles.statsContainer}>
          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Surface style={styles.statIconContainer} elevation={1}>
                <IconButton
                  icon="account-group"
                  size={32}
                  iconColor="#4ECDC4"
                />
              </Surface>
              <Text variant="displaySmall" style={styles.statNumber}>
                {state.contacts.length}
              </Text>
              <Text variant="bodyMedium" style={styles.statLabel}>
                Total Contacts
              </Text>
            </Card.Content>
          </Card>

          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Surface style={styles.statIconContainer} elevation={1}>
                <IconButton
                  icon="bell"
                  size={32}
                  iconColor="#45B7D1"
                />
              </Surface>
              <Text variant="displaySmall" style={styles.statNumber}>
                {actions.getActiveReminderInstances().length}
              </Text>
              <Text variant="bodyMedium" style={styles.statLabel}>
                Active Reminders
              </Text>
            </Card.Content>
          </Card>
        </View>

        <Card style={styles.comingSoonCard}>
          <Card.Content>
            <View style={styles.comingSoonHeader}>
              <IconButton
                icon="rocket-launch"
                size={24}
                iconColor="#FF6B6B"
              />
              <Text variant="titleLarge" style={styles.comingSoonTitle}>
                Coming Soon
              </Text>
            </View>
            <Text variant="bodyMedium" style={styles.comingSoonText}>
              • Upcoming reminders with smart notifications{'\n'}
              • Recent activity timeline{'\n'}
              • Quick actions for common tasks{'\n'}
              • Relationship insights and analytics{'\n'}
              • Circle-based interaction suggestions
            </Text>
          </Card.Content>
        </Card>

        <View style={styles.componentContainer}>
          <CircleStatistics contacts={state.contacts} />
        </View>

        <View style={styles.componentContainer}>
          <InteractionAnalytics
            contacts={state.contacts}
            interactions={state.interactions}
          />
        </View>

        <Card style={styles.tipsCard}>
          <Card.Content>
            <View style={styles.tipsHeader}>
              <IconButton
                icon="lightbulb"
                size={24}
                iconColor="#96CEB4"
              />
              <Text variant="titleMedium" style={styles.tipsTitle}>
                Quick Tips
              </Text>
            </View>
            <Text variant="bodyMedium" style={styles.tipsText}>
              • Add contacts to your circles to get started{'\n'}
              • Log interactions to track your relationships{'\n'}
              • Check the Contacts tab to manage your network
            </Text>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  welcomeCard: {
    marginBottom: 16,
    elevation: 2,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  subtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    elevation: 2,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  statIconContainer: {
    borderRadius: 50,
    marginBottom: 8,
  },
  statNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  statLabel: {
    opacity: 0.7,
  },
  comingSoonCard: {
    marginBottom: 16,
    elevation: 2,
  },
  comingSoonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  comingSoonTitle: {
    fontWeight: 'bold',
    marginLeft: 8,
  },
  comingSoonText: {
    lineHeight: 24,
    opacity: 0.8,
  },
  tipsCard: {
    marginBottom: 16,
    elevation: 2,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipsTitle: {
    fontWeight: 'bold',
    marginLeft: 8,
  },
  tipsText: {
    lineHeight: 24,
    opacity: 0.8,
  },
  componentContainer: {
    marginBottom: 16,
  },
});
