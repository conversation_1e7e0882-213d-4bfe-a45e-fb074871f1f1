# Persistence Model

## Overview

Relateful.app uses a local-first persistence model built on React Native's AsyncStorage for cross-platform data storage. The app maintains data locally on the device without requiring server synchronization, ensuring privacy and offline functionality.

## Storage Architecture

### Storage Technology
- **Primary Storage**: AsyncStorage (React Native)
- **Platform Support**: iOS, Android, Web
- **Data Format**: JSON serialization with ISO date strings
- **Backup Strategy**: Local device storage only (no cloud sync in MVP)

### Storage Keys
```javascript
const STORAGE_KEYS = {
  CONTACTS: '@relateful_contacts',
  INTERACTIONS: '@relateful_interactions',
  USER_PREFERENCES: '@relateful_preferences',
  REMINDER_INSTANCES: '@relateful_reminder_instances'
};

// Legacy keys (cleaned up on app load)
const LEGACY_KEYS = {
  REMINDERS: '@relateful_reminders',
  SNOOZE_DATA: '@relateful_snooze_data'
};
```

## Data Models

### Contact
The primary entity representing a person in the user's network.

```javascript
class Contact {
  id: string                    // Generated: 'contact_' + timestamp + '_' + random
  name: string                  // Required: Person's display name
  phone: string | null          // Optional: Phone number
  email: string | null          // Optional: Email address
  circle: string                // Required: 'support' | 'sympathy' | 'active' | 'full'
  categories: string[]          // Array of: 'friend' | 'coworker' | 'hobby' | 'acquaintance'
  lastInteracted: Date | null   // Date of most recent interaction
  recurringReminder: ReminderRule | null  // Embedded reminder configuration
  notes: string                 // Optional: Free-form notes
  createdAt: Date              // Auto-generated creation timestamp
  updatedAt: Date              // Auto-updated modification timestamp
}
```

**Validation Rules:**
- `name`: Required, non-empty string
- `phone`: Optional, must match pattern `/^[\+]?[1-9][\d]{0,15}$/`
- `email`: Optional, must match pattern `/^[^\s@]+@[^\s@]+\.[^\s@]+$/`
- `circle`: Must be one of the defined CIRCLE_TYPES
- `categories`: Array filtered to valid CATEGORY_TYPES only

### Interaction
Records of communication or contact with a person.

```javascript
class Interaction {
  id: string                    // Generated: 'interaction_' + timestamp + '_' + random
  contactId: string             // Required: Foreign key to Contact.id
  type: string                  // Required: 'call' | 'text' | 'meeting' | 'other'
  date: Date                    // Required: When the interaction occurred
  duration: number | null       // Optional: Duration in minutes (for calls/meetings)
  quality: string               // Required: 'brief' | 'good' | 'deep'
  mood: string                  // Required: 'positive' | 'neutral' | 'challenging'
  notes: string                 // Optional: Free-form notes about the interaction
  createdAt: Date              // Auto-generated creation timestamp
}
```

**Validation Rules:**
- `contactId`: Must reference existing Contact.id
- `type`: Must be one of the defined interaction types
- `date`: Must be valid date, validated with regex `/^\d{4}-\d{2}-\d{2}$/`
- `duration`: If provided, must be positive number
- `quality`: Must be 'brief', 'good', or 'deep'
- `mood`: Must be 'positive', 'neutral', or 'challenging'

### ReminderRule
Embedded within Contact to define recurring reminder frequency.

```javascript
class ReminderRule {
  id: string                    // Generated: 'rule_' + timestamp + '_' + random
  contactId: string             // Required: Foreign key to Contact.id
  type: string                  // Always 'recurring'
  frequency: number             // Required: Days between reminders (derived from circle)
  updatedDate: Date            // Auto-updated when rule changes
}
```

**Business Logic:**
- Frequency is automatically derived from the contact's Dunbar circle
- Support: 7 days, Sympathy: 14 days, Active: 30 days, Full: 90 days
- No custom frequencies allowed (simplified from original design)

### ReminderInstance
Stored reminder instances representing actual reminder occurrences.

```javascript
class ReminderInstance {
  id: string                    // Generated: 'instance_' + timestamp + '_' + random
  contactId: string             // Required: Foreign key to Contact.id
  reminderDate: Date            // Required: When the reminder is due
  completedDate: Date | null    // When marked complete, null if pending
  snoozedUntilDate: Date | null // When snooze expires, null if not snoozed
  reminderType: string          // Required: 'RECURRING' | 'ONCE'
  reminderRuleId: string | null // References ReminderRule.id for recurring, null for one-off
  state: string                 // Derived: 'PENDING' | 'COMPLETED' (from completedDate)
  createdAt: Date              // Auto-generated creation timestamp
  updatedAt: Date              // Auto-updated modification timestamp
}
```

**Validation Rules:**
- `contactId`: Must reference existing Contact.id
- `reminderDate`: Must be valid date
- `reminderType`: Must be 'RECURRING' or 'ONCE'
- `reminderRuleId`: Must reference existing ReminderRule.id if reminderType is 'RECURRING'
- `state`: Automatically derived from completedDate (null = 'PENDING', date = 'COMPLETED')

**Business Logic:**
- Only one pending reminder instance per contact at any time
- Completed instances are kept for historical tracking
- Snoozing updates the existing instance's snoozedUntilDate
- New recurring instances generated when current one is completed

### SnoozeData (Legacy - Migrated to ReminderInstance)
**Note: This model is deprecated and replaced by snooze functionality within ReminderInstance.**

Legacy snooze information that will be migrated to ReminderInstance.snoozedUntilDate during the migration process.

### UserPreferences
Application settings and user state.

```javascript
interface UserPreferences {
  hasCompletedOnboarding: boolean  // Whether user has completed initial setup
  // Additional preferences can be added here
}
```

## Data Relationships

### Entity Relationship Diagram
```
Contact (1) ←→ (0..1) ReminderRule [embedded]
Contact (1) ←→ (0..n) Interaction [foreign key: contactId]
Contact (1) ←→ (0..n) ReminderInstance [foreign key: contactId]
ReminderRule (1) ←→ (0..n) ReminderInstance [foreign key: reminderRuleId]
```

### Referential Integrity
- **Cascade Delete**: When a contact is deleted, all associated interactions and reminder instances are automatically deleted
- **Reminder Instance Cleanup**: Completed instances older than 1 year are automatically cleaned up
- **Snooze Integration**: Snooze functionality integrated into ReminderInstance model
- **Legacy Cleanup**: Old reminder storage formats are cleaned up on app initialization
- **Single Pending Rule**: Only one pending reminder instance per contact at any time

## Constants and Configuration

### Dunbar Circles
```javascript
const DUNBAR_CIRCLES = {
  support: { limit: 5, frequency: 7, color: '#FF6B6B' },
  sympathy: { limit: 15, frequency: 14, color: '#4ECDC4' },
  active: { limit: 50, frequency: 30, color: '#45B7D1' },
  full: { limit: 150, frequency: 90, color: '#96CEB4' }
};
```

### Contact Categories
```javascript
const CONTACT_CATEGORIES = {
  friend: { name: 'Friend', color: '#FF6B6B', icon: 'heart' },
  coworker: { name: 'Coworker', color: '#4ECDC4', icon: 'briefcase' },
  hobby: { name: 'Hobby', color: '#45B7D1', icon: 'gamepad-variant' },
  acquaintance: { name: 'Acquaintance', color: '#96CEB4', icon: 'account' }
};
```

### Interaction Types
```javascript
const INTERACTION_TYPES = {
  call: { name: 'Phone Calls', icon: 'phone', color: '#4CAF50' },
  text: { name: 'Text Messages', icon: 'message-text', color: '#2196F3' },
  meeting: { name: 'In-Person', icon: 'account-group', color: '#FF9800' },
  other: { name: 'Other', icon: 'dots-horizontal', color: '#9E9E9E' }
};
```

## Data Operations

### CRUD Operations
All data operations are handled through service classes:
- **DataService**: Primary CRUD for contacts, interactions, reminder instances, and preferences
- **MigrationService**: Handles data migrations between schema versions
- **TestDataService**: Sample data generation for development

### Data Serialization
- **Dates**: Stored as ISO strings, converted to Date objects on load
- **Objects**: JSON serialization with `toJSON()` methods on model classes
- **Validation**: Client-side validation on all inputs before storage

### Performance Considerations
- **Stored Instances**: Reminder instances pre-computed and stored for fast access
- **Batch Operations**: Multiple storage operations use Promise.all for efficiency
- **Memory Management**: Large datasets filtered and paginated in UI components
- **Cleanup**: Automatic removal of old completed instances and legacy storage
- **Indexing**: Efficient querying by contactId and reminderDate

## Migration Strategy

### Calculated to Stored Instance Migration
The app migrates from calculated reminders to stored reminder instances:

**Migration Process:**
1. **Data Version Check**: Check `userPreferences.dataVersion` to determine if migration is needed
2. **Contact Processing**: For each contact with a recurringReminder:
   - Calculate current reminder due date using existing logic
   - Create ReminderInstance with appropriate state (PENDING/COMPLETED)
   - Migrate snooze data if present
3. **Snooze Data Migration**: Convert existing snooze data to ReminderInstance.snoozedUntilDate
4. **Legacy Cleanup**: Remove old snooze data storage
5. **Version Update**: Set `userPreferences.dataVersion = 2` to mark migration complete

**Migration Logic for Reminder Dates:**
```javascript
// For contacts with no interactions
if (!lastInteractionDate && !lastReminderCompleted) {
  reminderDate = addDays(contact.createdAt, frequency);
}
// For contacts with interactions but no completed reminders
else if (lastInteractionDate && !lastReminderCompleted) {
  reminderDate = addDays(lastInteractionDate, frequency);
}
// For contacts with completed reminders
else if (lastReminderCompleted) {
  reminderDate = addDays(lastReminderCompleted, frequency);
}
```

### Reminder Instance Generation Logic

**Automatic Generation Triggers:**
1. **Contact Creation**: Generate initial reminder instance based on circle frequency
2. **Contact Circle Change**: Regenerate pending instances with new frequency
3. **Interaction Logging**: Complete current instance and generate next recurring instance
4. **Reminder Completion**: Generate next recurring instance if applicable

**Generation Algorithm:**
- **Initial Instance**: `reminderDate = contact.createdAt + circle.frequency`
- **Next Recurring**: `reminderDate = completedDate + circle.frequency`
- **After Interaction**: `reminderDate = interaction.date + circle.frequency`

**Edge Cases:**
- **Circle Change**: Update pending instance date, keep completed instances unchanged
- **Multiple Pending**: Prevent duplicate pending instances per contact
- **Retroactive Interactions**: Recalculate pending instance dates if interaction is earlier than expected

### Future Migrations
For future schema changes:
1. Add version field to stored data
2. Implement migration functions in MigrationService
3. Run migrations on app startup based on version comparison
4. Maintain backward compatibility during transition periods

## Security and Privacy

### Data Protection
- **Local Only**: All data stored locally on device
- **No Cloud Sync**: No data transmitted to external servers
- **User Control**: Users can clear all data through app settings
- **Platform Security**: Relies on platform-level app sandboxing

### Data Validation
- **Input Sanitization**: All user inputs validated before storage
- **Type Safety**: Model classes enforce data types and constraints
- **Error Handling**: Graceful degradation when data corruption occurs
