import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Appbar,
  TextInput,
  Button,
  SegmentedButtons,
  Chip,
  Text,
  Card,
  Surface,
  useTheme,
  Snackbar
} from 'react-native-paper';
import { useApp } from '../context/AppContext';
import { Contact, ReminderRule } from '../utils/dataModels';
import { DUNBAR_CIRCLES, CONTACT_CATEGORIES, CATEGORY_TYPES } from '../constants/dunbarCircles';
import { STATUS_COLORS } from '../constants/themeConstants';


export default function EditContactScreen({ navigation, route }) {
  const { actions, state } = useApp();
  const theme = useTheme();
  const { contact: contactParam } = route.params;

  // Get the actual Contact instance from state (contactParam might be serialized JSON)
  const contact = state.contacts.find(c => c.id === contactParam.id) || contactParam;
  
  // Form state - initialize with existing contact data
  const [formData, setFormData] = useState({
    name: contact.name || '',
    phone: contact.phone || '',
    email: contact.email || '',
    circle: contact.circle || 'full',
    categories: contact.categories || [],
    notes: contact.notes || ''
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Create theme-aware styles
  const styles = createStyles(theme);

  // Circle selection
  const circleOptions = Object.keys(DUNBAR_CIRCLES).map(key => ({
    value: key,
    label: DUNBAR_CIRCLES[key].name,
    icon: 'circle'
  }));

  const handleCircleChange = (value) => {
    setFormData(prev => ({
      ...prev,
      circle: value
    }));
  };

  // Category selection
  const toggleCategory = (category) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category]
    }));
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (formData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }
    
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleUpdate = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Update recurring reminder frequency if circle changed
      let updatedRecurringReminder = contact.recurringReminder;
      if (formData.circle !== contact.circle && contact.recurringReminder) {
        const newCircle = DUNBAR_CIRCLES[formData.circle];
        updatedRecurringReminder = new ReminderRule({
          ...contact.recurringReminder,
          frequency: newCircle.frequency,
          updatedDate: new Date()
        });
      }

      const updatedContact = new Contact({
        ...contact,
        name: formData.name.trim(),
        phone: formData.phone.trim() || null,
        email: formData.email.trim() || null,
        circle: formData.circle,
        categories: formData.categories,
        notes: formData.notes.trim(),
        recurringReminder: updatedRecurringReminder,
        updatedAt: new Date()
      });

      await actions.updateContact(updatedContact);

      // Show success message
      setSnackbarMessage('Contact updated successfully!');
      setSnackbarVisible(true);

      // Navigate back after a short delay to let user see the toast
      setTimeout(() => {
        navigation.goBack();
      }, 500);
    } catch (error) {
      setSnackbarMessage('Failed to update contact. Please try again.');
      setSnackbarVisible(true);
      console.error('Error updating contact:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle contact deletion
  const handleDelete = () => {
    console.log('Delete button pressed for contact:', contact.name);

    // For web compatibility, let's use window.confirm first
    if (typeof window !== 'undefined' && window.confirm) {
      const confirmed = window.confirm(`Are you sure you want to delete ${contact.name}? This action cannot be undone.`);
      if (confirmed) {
        performDelete();
      }
    } else {
      // Fallback to React Native Alert
      Alert.alert(
        'Delete Contact',
        `Are you sure you want to delete ${contact.name}? This action cannot be undone.`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => console.log('Delete cancelled')
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: performDelete
          }
        ]
      );
    }
  };

  const performDelete = async () => {
    console.log('Delete confirmed, starting deletion...');
    setLoading(true);
    try {
      await actions.deleteContact(contact.id);
      console.log('Contact deleted successfully');

      if (typeof window !== 'undefined' && window.alert) {
        window.alert('Contact deleted successfully.');
        // Navigate back to contacts list instead of contact detail
        navigation.navigate('ContactsList');
      } else {
        Alert.alert(
          'Deleted',
          'Contact deleted successfully.',
          [
            {
              text: 'OK',
              onPress: () => {
                console.log('Navigating to contacts list after deletion');
                // Navigate back to contacts list instead of contact detail
                navigation.navigate('ContactsList');
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error deleting contact:', error);
      if (typeof window !== 'undefined' && window.alert) {
        window.alert('Failed to delete contact. Please try again.');
      } else {
        Alert.alert('Error', 'Failed to delete contact. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryChips = () => {
    return (
      <View style={styles.categoriesContainer}>
        <Text variant="titleMedium" style={styles.sectionLabel}>Categories (Optional)</Text>
        <View style={styles.chipsContainer}>
          {CATEGORY_TYPES.map(category => {
            const categoryInfo = CONTACT_CATEGORIES[category];
            const isSelected = formData.categories.includes(category);

            return (
              <Chip
                key={category}
                selected={isSelected}
                onPress={() => toggleCategory(category)}
                style={[
                  styles.chip,
                  isSelected && { backgroundColor: categoryInfo.color + '20' }
                ]}
                textStyle={[
                  styles.chipText,
                  isSelected && { color: categoryInfo.color, fontWeight: 'bold' }
                ]}
              >
                {categoryInfo.name}
              </Chip>
            );
          })}
        </View>
      </View>
    );
  };

  const renderReminderInfo = () => {
    const circle = DUNBAR_CIRCLES[formData.circle];

    return (
      <View style={styles.reminderSection}>
        <Text variant="titleMedium" style={styles.sectionLabel}>Reminder</Text>

        <View style={styles.reminderInfoContainer}>
          <Text variant="bodyMedium" style={styles.reminderInfoText}>
            This contact will use the {circle.name} frequency: every {circle.frequency} days
          </Text>
        </View>

        <Text variant="bodySmall" style={styles.reminderDescription}>
          Reminder frequency is automatically determined by the Dunbar circle.
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="Edit Contact" />
      </Appbar.Header>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.formCard}>
          <Card.Content>
            <TextInput
              label="Name *"
              placeholder="Enter contact name"
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              error={!!errors.name}
              left={<TextInput.Icon icon="account" />}
              style={styles.input}
            />
            {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}

            <TextInput
              label="Phone Number"
              placeholder="Enter phone number"
              value={formData.phone}
              onChangeText={(text) => setFormData(prev => ({ ...prev, phone: text }))}
              error={!!errors.phone}
              keyboardType="phone-pad"
              left={<TextInput.Icon icon="phone" />}
              style={styles.input}
            />
            {errors.phone && <Text style={styles.errorText}>{errors.phone}</Text>}

            <TextInput
              label="Email"
              placeholder="Enter email address"
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              error={!!errors.email}
              keyboardType="email-address"
              autoCapitalize="none"
              left={<TextInput.Icon icon="email" />}
              style={styles.input}
            />
            {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
          
          <View style={styles.circleSection}>
            <Text variant="titleMedium" style={styles.sectionLabel}>Dunbar Circle *</Text>
            <SegmentedButtons
              value={formData.circle}
              onValueChange={handleCircleChange}
              buttons={circleOptions}
              style={styles.segmentedButtons}
            />
            <Text variant="bodySmall" style={styles.circleDescription}>
              {DUNBAR_CIRCLES[formData.circle].description}
            </Text>
          </View>
          
          {renderCategoryChips()}

          {renderReminderInfo()}

          <TextInput
            label="Notes"
            placeholder="Add any notes about this contact"
            value={formData.notes}
            onChangeText={(text) => setFormData(prev => ({ ...prev, notes: text }))}
            multiline
            numberOfLines={3}
            left={<TextInput.Icon icon="note-text" />}
            style={styles.input}
          />
          
            <Button
              mode="contained"
              onPress={handleUpdate}
              loading={loading}
              disabled={loading}
              style={styles.updateButton}
            >
              Update Contact
            </Button>

            <Button
              mode="outlined"
              onPress={() => {
                console.log('Delete button onPress triggered');
                handleDelete();
              }}
              disabled={loading}
              buttonColor={STATUS_COLORS.error}
              style={styles.deleteButton}
            >
              Delete Contact
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        action={{
          label: 'OK',
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formCard: {
    elevation: 2,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    color: theme.colors.error,
    fontSize: 12,
    marginBottom: 16,
    marginLeft: 16,
  },
  sectionLabel: {
    marginBottom: 12,
    marginTop: 16,
  },
  circleSection: {
    marginVertical: 16,
  },
  segmentedButtons: {
    marginBottom: 12,
  },
  circleDescription: {
    fontStyle: 'italic',
    textAlign: 'center',
    opacity: 0.7,
  },
  categoriesContainer: {
    marginVertical: 16,
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginRight: 8,
    marginBottom: 8,
  },
  chipText: {
    fontSize: 14,
  },
  updateButton: {
    marginTop: 24,
    marginBottom: 12,
  },
  deleteButton: {
    marginBottom: 16,
  },
  reminderSection: {
    marginVertical: 16,
  },
  reminderInfoContainer: {
    padding: 12,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 8,
    marginBottom: 12,
  },
  reminderInfoText: {
    textAlign: 'center',
    fontWeight: '500',
  },
  reminderDescription: {
    fontStyle: 'italic',
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 18,
  },
});
