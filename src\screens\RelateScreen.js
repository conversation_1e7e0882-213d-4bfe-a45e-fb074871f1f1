import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Appbar, useTheme } from 'react-native-paper';
import { useApp } from '../context/AppContext';
import UpcomingReminders from '../components/UpcomingReminders';
import RelationshipOverview from '../components/RelationshipOverview';
// import CircleMaintenance from '../components/CircleMaintenance';

export default function RelateScreen({ navigation }) {
  const { state } = useApp();
  const theme = useTheme();

  // Create theme-aware styles
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title="Relate" />
      </Appbar.Header>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <RelationshipOverview
          contacts={state.contacts}
          interactions={state.interactions}
        />

        <UpcomingReminders navigation={navigation} />

        {/* <CircleMaintenance
          contacts={state.contacts}
          interactions={state.interactions}
        /> */}
      </ScrollView>
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  welcomeCard: {
    marginBottom: 16,
    elevation: 2,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  subtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    elevation: 2,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  statIconContainer: {
    borderRadius: 50,
    marginBottom: 8,
  },
  statNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
    color: theme.colors.onSurface,
  },
  statLabel: {
    opacity: 0.7,
  },
  actionCard: {
    marginBottom: 16,
    elevation: 2,
  },
  actionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  actionTitle: {
    fontWeight: 'bold',
    marginLeft: 8,
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    marginVertical: 4,
  },
  insightCard: {
    marginBottom: 16,
    elevation: 2,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  insightTitle: {
    fontWeight: 'bold',
    marginLeft: 8,
  },
  insightText: {
    lineHeight: 24,
    opacity: 0.8,
    marginBottom: 16,
  },
  insightButton: {
    alignSelf: 'flex-start',
  },
  philosophyCard: {
    marginBottom: 16,
    elevation: 2,
  },
  philosophyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  philosophyTitle: {
    fontWeight: 'bold',
    marginLeft: 8,
  },
  philosophyText: {
    lineHeight: 24,
    opacity: 0.8,
  },
});
