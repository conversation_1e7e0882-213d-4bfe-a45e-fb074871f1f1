import React, { useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import {
  Card,
  Text,
  Surface,
  IconButton,
  useTheme
} from 'react-native-paper';
import { getRelationshipOverview } from '../utils/relationshipOverview';
import { STATUS_COLORS } from '../constants/themeConstants';

export default function RelationshipOverview({ contacts, interactions }) {
  const theme = useTheme();

  // Calculate overview metrics
  const overview = useMemo(() => {
    return getRelationshipOverview(contacts, interactions);
  }, [contacts, interactions]);

  // Get trend icon and color
  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up': return 'trending-up';
      case 'down': return 'trending-down';
      default: return 'trending-neutral';
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'up': return STATUS_COLORS.success;
      case 'down': return STATUS_COLORS.error;
      default: return theme.colors.outline;
    }
  };

  // Render metric item
  const renderMetric = (title, value, icon, subtitle = null, showTrend = false) => (
    <Surface style={styles.metricItem} elevation={1}>
      <View style={styles.metricHeader}>
        <IconButton 
          icon={icon} 
          size={20}
          iconColor={theme.colors.primary}
          style={styles.metricIcon}
        />
        {showTrend && (
          <IconButton 
            icon={getTrendIcon(overview.trend)} 
            size={16}
            iconColor={getTrendColor(overview.trend)}
            style={styles.trendIcon}
          />
        )}
      </View>
      <Text variant="headlineSmall" style={styles.metricValue}>
        {value}
      </Text>
      <Text variant="bodySmall" style={styles.metricTitle}>
        {title}
      </Text>
      {subtitle && (
        <Text variant="bodySmall" style={styles.metricSubtitle}>
          {subtitle}
        </Text>
      )}
    </Surface>
  );

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <Text variant="titleLarge" style={styles.title}>
            Relationship Overview
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            Your connection activity at a glance
          </Text>
        </View>

        <View style={styles.metricsGrid}>
          {renderMetric(
            'Total Contacts',
            overview.totalContacts,
            'account-group'
          )}
          
          {renderMetric(
            'This Week',
            overview.weeklyInteractions,
            'calendar-week',
            'interactions',
            true
          )}
          
          {renderMetric(
            'This Month',
            overview.monthlyInteractions,
            'calendar-month',
            'interactions'
          )}
          
          {renderMetric(
            'On Track',
            `${overview.onTrackScore}%`,
            'target',
            'relationships'
          )}
        </View>

        {/* Trend insight */}
        <View style={styles.insightContainer}>
          <Surface style={styles.insightSurface} elevation={1}>
            <View style={styles.insightHeader}>
              <IconButton 
                icon={getTrendIcon(overview.trend)} 
                size={20}
                iconColor={getTrendColor(overview.trend)}
                style={styles.insightIcon}
              />
              <Text variant="bodyMedium" style={styles.insightText}>
                {getInsightMessage(overview.trend, overview.weeklyInteractions)}
              </Text>
            </View>
          </Surface>
        </View>
      </Card.Content>
    </Card>
  );
}

// Helper function to get insight message
const getInsightMessage = (trend, weeklyInteractions) => {
  if (weeklyInteractions === 0) {
    return "No interactions this week. Consider reaching out to someone!";
  }
  
  switch (trend) {
    case 'up':
      return "Great! You're connecting more than last week.";
    case 'down':
      return "You've been less active than last week. Consider reaching out.";
    default:
      return "Your connection activity is steady.";
  }
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    opacity: 0.7,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  metricItem: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricIcon: {
    margin: 0,
  },
  trendIcon: {
    margin: 0,
    marginLeft: 4,
  },
  metricValue: {
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  metricTitle: {
    textAlign: 'center',
    fontWeight: '500',
  },
  metricSubtitle: {
    textAlign: 'center',
    opacity: 0.6,
    marginTop: 2,
  },
  insightContainer: {
    marginTop: 8,
  },
  insightSurface: {
    borderRadius: 8,
    padding: 12,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  insightIcon: {
    margin: 0,
    marginRight: 8,
  },
  insightText: {
    flex: 1,
    lineHeight: 20,
  },
});
