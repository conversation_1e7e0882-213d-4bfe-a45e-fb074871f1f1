# Log Detail

This screen displays the detailed view of a specific interaction, allowing users to view all information about the interaction and edit it if needed.

## Navigation
- **Entry Points**: 
  - Accessible by tapping an interaction item from the LogScreen
  - Accessible by tapping an interaction item from the ContactDetailScreen's InteractionHistory component
- **Exit**: 
  - Back navigation returns to the previous screen (LogScreen or ContactDetailScreen)
  - Edit button navigates to EditInteractionScreen

## Screen Layout

### Header
- **App Bar**: Standard Material Design app bar with back button and title "Interaction Detail"
- **Title**: Shows "Interaction Detail" as the screen title

### Content Sections

#### 1. Contact Information Section
Display the contact this interaction belongs to:
- **Contact Avatar**: Colored circle with contact's first initial (using Dunbar circle color)
- **Contact Name**: Contact's full name prominently displayed
- **Contact Circle**: Dunbar circle name with color indicator
- **Navigation**: Tappable area that navigates to ContactDetailScreen for this contact

#### 2. Interaction Overview Section
Display core interaction information:
- **Interaction Type**: Icon and name (e.g., "Phone Call", "Text Message", "In-Person", "Other")
- **Date**: Both relative date (e.g., "3 days ago") and absolute date (e.g., "December 25, 2024")
- **Duration**: Display duration in minutes for calls and meetings (if available)
- **Quality Indicator**: Visual representation of interaction quality (Brief, Good, Deep)
- **Mood Indicator**: Color-coded mood indicator (Positive, Neutral, Challenging)

#### 3. Interaction Details Section
Display additional interaction information:
- **Notes**: Full notes text from the interaction (if available)
- **Created Date**: When this interaction was logged into the system
- **Last Modified**: If the interaction was edited, show when it was last modified

#### 4. Metadata Section (Optional)
Display technical details in a collapsible or subtle section:
- **Interaction ID**: For debugging purposes
- **Contact ID**: Reference to the associated contact

### Material Design Elements
- **Card Layout**: Each section organized in Material Design cards
- **Typography**: Material Design text hierarchy with proper contrast
- **Color Coding**: Consistent with interaction types, moods, and Dunbar circles
- **Spacing**: Proper margins and padding for readability
- **Floating Action Button**: Edit button in bottom right corner with pencil icon

### Data Display Patterns

#### Quality Display
- **Brief**: Light gray indicator with "Brief" label
- **Good**: Default state, may not need special indicator
- **Deep**: Green indicator with "Deep" label

#### Mood Display
- **Positive**: Green color indicator
- **Neutral**: Orange color indicator  
- **Challenging**: Red color indicator

#### Duration Display
- Only show for interaction types that support duration (calls, meetings)
- Format: "45 minutes" or "1 hour 15 minutes"
- Hide section if no duration is recorded

#### Date Display
- **Primary**: Relative date (e.g., "3 days ago", "Yesterday", "Today")
- **Secondary**: Absolute date (e.g., "December 25, 2024")
- **Time**: Include time if it's from today (e.g., "Today at 2:30 PM")

### Edit Functionality
- **Edit Button**: Floating Action Button in bottom right corner
- **Icon**: Pencil icon (Material Design standard)
- **Navigation**: Takes user to EditInteractionScreen with current interaction data pre-filled
- **Behavior**: Similar to ContactDetailScreen's edit button pattern

### Empty States
- **No Notes**: If no notes are provided, show placeholder text or hide the notes section
- **No Duration**: Hide duration section for interaction types that don't support it or when not provided

### Accessibility
- **Screen Reader**: All elements properly labeled for screen readers
- **Touch Targets**: Minimum 44dp touch targets for interactive elements
- **Color Contrast**: Proper contrast ratios for all text and indicators
- **Focus Management**: Proper focus order for keyboard navigation

### Error Handling
- **Missing Contact**: If the associated contact is deleted, show placeholder contact information
- **Invalid Data**: Gracefully handle any missing or corrupted interaction data
- **Network Issues**: Handle any data loading issues with appropriate error states

## Technical Requirements

### Data Fields to Display
Reuse all fields from the Interaction data model:
- `id` - Interaction identifier
- `contactId` - Associated contact identifier  
- `type` - Interaction type ('call', 'text', 'meeting', 'other')
- `date` - When the interaction occurred
- `duration` - Duration in minutes (for calls/meetings)
- `quality` - Interaction quality ('brief', 'good', 'deep')
- `mood` - Interaction mood ('positive', 'neutral', 'challenging')
- `notes` - User notes about the interaction
- `createdAt` - When the interaction was logged

### Navigation Parameters
- **Required**: `interaction` object containing all interaction data
- **Optional**: `contact` object (if available, to avoid lookup)
- **Return**: Should return to the calling screen (LogScreen or ContactDetailScreen)

### State Management
- **Read-Only**: This screen is primarily for viewing interaction details
- **Data Sync**: Should reflect any updates if returning from EditInteractionScreen
- **Contact Lookup**: Efficiently lookup contact information for display

This screen serves as a comprehensive view of interaction details while maintaining consistency with the app's Material Design patterns and providing easy access to editing functionality.

## Related Screens

### EditInteractionScreen
The edit functionality should navigate to an EditInteractionScreen that:
- **Reuses Fields**: Uses the same form fields as LogInteractionScreen (type, date, duration, quality, mood, notes)
- **Pre-filled Data**: All fields are pre-populated with current interaction data
- **Save Button**: "Update Interaction" instead of "Log Interaction"
- **Validation**: Same validation rules as LogInteractionScreen
- **Navigation**: Returns to LogDetailScreen after successful update
- **Contact Display**: Shows the contact name in a read-only header (cannot change which contact the interaction belongs to)

The EditInteractionScreen should maintain the same UI patterns and field layouts as LogInteractionScreen for consistency, but with the context of editing an existing interaction rather than creating a new one.
