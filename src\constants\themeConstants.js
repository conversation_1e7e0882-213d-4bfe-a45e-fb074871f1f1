/**
 * Centralized theme constants for consistent styling across the app
 * Use these instead of hardcoded colors and values
 */

// Status colors that should be consistent across the app
export const STATUS_COLORS = {
  success: '#4CAF50',
  warning: '#FF9800', 
  error: '#FF6B6B',
  info: '#2196F3',
  overdue: '#FF6B6B',
  upcoming: '#FF9800',
  good: '#4CAF50'
};

// Common spacing values
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32
};

// Common elevation values
export const ELEVATION = {
  none: 0,
  low: 1,
  medium: 2,
  high: 4,
  highest: 8
};

// Button styling constants
export const BUTTON_STYLES = {
  marginVertical: SPACING.sm,
  marginHorizontal: 0
};

// Card styling constants  
export const CARD_STYLES = {
  marginBottom: SPACING.lg,
  elevation: ELEVATION.medium
};

// Common border radius values
export const BORDER_RADIUS = {
  small: 4,
  medium: 8,
  large: 12,
  round: 50
};

// Typography weights
export const FONT_WEIGHTS = {
  normal: '400',
  medium: '500', 
  semibold: '600',
  bold: '700'
};
