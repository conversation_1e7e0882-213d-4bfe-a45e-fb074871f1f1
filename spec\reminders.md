This spec describes how reminders work. Both recurring and one-off reminder exist.

## Contact Reminder Fields

Each contact maintains three key fields for reminder tracking:

- **lastInteracted**: For which date the latest (not last) interaction was logged with this contact
- **nextReminderScheduled**: For which date the next reminder is scheduled to appear
- **lastReminderCompleted**: For which date the last reminder was marked as completed

These fields are essential for the reminder system to function correctly and provide accurate scheduling.

## Reminder Logic

Recurring reminders are applied in priority: Each Dunbar circle defines its own reminder frequency as follows:

### Dunbar Circle Frequencies
- **Support Clique**: 7 days (Weekly) - Your closest relationships
- **Sympathy Group**: 14 days (Bi-weekly) - Close friends you turn to for support
- **Active Network**: 30 days (Monthly) - Friends you see regularly
- **Full Network**: 90 days (Quarterly) - Acquaintances and extended network

Reminder frequency is automatically determined by the contact's Dunbar circle and cannot be customized individually.

## Automatic Reminder Creation

When a new contact is created, the system automatically:
1. **Creates a Recurring Reminder Rule** based on the contact's Dunbar circle
2. **Stores the rule** within the contact's `recurringReminder` attribute
3. **Sets the frequency** according to the circle's default frequency (as defined in `DUNBAR_CIRCLES` constants)

### Implementation Details
The automatic reminder creation is implemented in the `addContact` action in `AppContext.js`:
- Imports the `ReminderRule` class and `DUNBAR_CIRCLES` constants
- Creates a new `ReminderRule` instance with the contact ID and circle frequency
- Adds the reminder rule to the contact's `recurringReminder` attribute

### Circle-to-Frequency Mapping
The frequencies are automatically applied based on the contact's assigned Dunbar circle:
- **Support Clique** (`support`): 7 days (Weekly)
- **Sympathy Group** (`sympathy`): 14 days (Bi-weekly)
- **Active Network** (`active`): 30 days (Monthly)
- **Full Network** (`full`): 90 days (Quarterly)

This ensures every contact has an appropriate reminder frequency from the moment they're added to the system.

## Storage

### Contact Fields
Each contact stored in @relateful_contacts includes these reminder-related fields:
- **lastInteracted**: Date of the latest interaction logged (null if no interactions)
- **lastReminderCompleted**: Date when the last reminder was marked as completed (null if none completed)
- **recurringReminder**: ReminderRule object containing the recurring reminder configuration

### Reminder Rules
The storage maintains a single reminder rule for each contact - they are stored within the key @relateful_contacts within the "recurringReminder" attribute. A reminder has the following properties:
- id: Unique ID
- contactId: The contact this reminder is for
- type: "recurring" 
- updatedDate
- frequency: number (days) - only for recurring reminders

## Reminders
The instances of reminders are either derived from Reminder Rules or one-off reminders. They are stored in @relateful_reminder_instances. A reminder has the following properties:
- id: Unique ID of the instance
- reminderRuleId: null if one-off reminder
- contactId: The contact this reminder is for
- type: "recurring" or "one-off"
- dueDate
- completedDate: null if not completed
- snoozedUntilDate: null if not snoozed