# Edit Contact
This screen is accessible by clicking on the "Edit" button in the "Contact Detail" screen defined in contact_detail.md

Text Input Fields: At the top, there are standard input fields for basic contact information:

    Name: A required text field.
    Phone Number: A field for the contact's number.
    Email: A field for the contact's email address.

Dunbar Circle Selection: This required section uses a segmented button control to assign the contact to one of four groups: "Support Clique", "Sympathy Group", "Active Network", or "Full Network". Only one option can be selected. Helper text below explains the purpose of the groups.

Categories: An optional section where you can assign multiple tags or categories to the contact using selectable chips, such as "Friend", "Coworker", or "Hobby".

Add Reminder: Allows to add a oneoff reminder by picking of the following option:
  Tomorrow, Next Week, Next Month, Next Quarter, Next Year
On the right to it there is a button that sets this new reminder.

Notes: A simple text area at the bottom of the form for adding free-form notes about the contact.

Action Buttons: At the very bottom, there are two full-width buttons:

    Update Contact: A primary, light blue button to save changes.
    Delete Contact: A secondary, red button to remove the contact.

## Contact Editing Edge Cases and Reminder Instance Behavior

### Circle Changes

#### Scenario: Contact moved from one circle to another
**Example**: Contact moved from "Active" (30-day frequency) to "Support" (7-day frequency)

**Behavior**:
- Existing pending reminder instances remain unchanged
- New reminder instances generated after interactions will use the new circle's frequency
- No automatic regeneration of existing instances occurs

**Rationale**: Preserves user's existing reminder schedule while applying new frequency to future reminders.

#### Implementation Details:
- `syncContactReminderInstances()` is called when contact is saved
- Uses `REMINDER_CONFIG.AUTO_GENERATE.CONTACT_UPDATED = false` to prevent automatic regeneration
- Manual regeneration available through `actions.regenerateContactReminderInstances(contactId)`

### Reminder Frequency Changes

#### Scenario: Custom frequency modified
**Example**: Contact's custom frequency changed from 14 days to 21 days

**Behavior**:
- Current pending reminder instances remain at original frequency
- Next reminder instance (generated after interaction) uses new frequency
- Completed instances are not affected

**Edge Case**: If user wants immediate frequency change:
- Use `actions.regenerateContactReminderInstances(contactId)` to recreate all pending instances
- This will delete existing pending instances and create new ones with updated frequency

### Reminder Rule Addition/Removal

#### Scenario: Adding recurring reminder to contact without one
**Behavior**:
- New reminder instance generated immediately using contact's circle frequency
- Instance due date calculated from `lastInteracted` or `createdAt` date
- Uses `generateInitialReminderInstance()` function

#### Scenario: Removing recurring reminder from contact
**Behavior**:
- All pending reminder instances for the contact are deleted
- Completed instances remain for historical tracking
- Uses `cleanupContactReminderInstances(contactId, { keepCompleted: true })`

### Contact Deletion

#### Scenario: Contact is permanently deleted
**Behavior**:
- All reminder instances (pending and completed) are deleted
- Cascade delete handled in `AppContext.js` reducer
- No orphaned reminder instances remain

**Implementation**:
```javascript
case ACTION_TYPES.DELETE_CONTACT:
  return {
    ...state,
    contacts: state.contacts.filter(contact => contact.id !== action.payload),
    interactions: state.interactions.filter(interaction => interaction.contactId !== action.payload),
    reminderInstances: state.reminderInstances.filter(instance => instance.contactId !== action.payload)
  };
```

## Reminder Instance Synchronization

### Automatic Synchronization Events

1. **Contact Creation**: `generateInitialReminderInstance()` if contact has recurring reminder
2. **Contact Update**: No automatic regeneration (preserves existing schedule)
3. **Interaction Logging**: `handleInteractionLogged()` completes current instance and generates next
4. **Contact Deletion**: All instances deleted via cascade

### Manual Synchronization Options

1. **Regenerate Single Contact**: `actions.regenerateContactReminderInstances(contactId)`
2. **Regenerate All Contacts**: `actions.regenerateAllReminderInstances()`
3. **Sync Contact Instances**: `syncContactReminderInstances(contact, existingInstances)`

## Data Consistency Rules

### Reminder Instance Lifecycle

1. **Creation**: Instance created with deterministic ID `instance_{duetimestamp}_{contactId}`
2. **Pending State**: Instance remains pending until due date passes or user action
3. **Completion**: Instance marked complete when interaction logged
4. **Snoozing**: Instance `snoozedUntilDate` updated, state remains pending
5. **Cleanup**: Completed instances can be archived after configurable period

### Validation Rules

1. **Unique Instances**: No duplicate instances for same contact and due date
2. **Contact Reference**: All instances must reference existing contact
3. **Date Consistency**: Reminder date must be valid and in correct format
4. **State Integrity**: Instance state must be valid ('PENDING', 'COMPLETED', 'CANCELLED')

## Configuration Constants

### Reminder Generation Control
```javascript
REMINDER_CONFIG.AUTO_GENERATE = {
  CONTACT_CREATED: true,     // Generate instance when contact created
  CONTACT_UPDATED: false,    // Don't regenerate on contact update
  INTERACTION_LOGGED: true,  // Generate next instance after interaction
  CIRCLE_CHANGED: false      // Don't regenerate on circle change
};
```

### Reminder Display Settings
```javascript
REMINDER_CONFIG.HOME_PREVIEW_DAYS = 5;  // Show reminders due within 5 days
REMINDER_CONFIG.SNOOZE_VISIBILITY = true; // Show snoozed reminders in lists
```

## Error Handling

### Missing Contact Reference
**Scenario**: Reminder instance exists but contact was deleted outside normal flow
**Handling**: Instance is filtered out during display, cleanup job removes orphaned instances

### Invalid Reminder Dates
**Scenario**: Reminder instance has invalid or corrupted date
**Handling**: Instance is skipped during processing, error logged for investigation

### Duplicate Instances
**Scenario**: Multiple instances exist for same contact and due date
**Handling**: Deterministic ID prevents creation, existing duplicates resolved by keeping most recent

## Migration Considerations

### Legacy Data Handling
- Old calculated reminders are not migrated to instances
- Fresh start approach with new reminder instance system
- Legacy snooze data is not transferred to new instances

### Backward Compatibility
- Legacy reminder calculation functions remain available but unused
- Old storage keys maintained during transition period
- Gradual cleanup of legacy data through normal app usage