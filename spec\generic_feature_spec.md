# **Relateful.app: Prototype & Feature Specification**

This document outlines the features and functionality for the initial prototype and subsequent versions of Relateful.app. Features are grouped by area and prioritized to guide a phased development approach.  
**Priority Levels:**

* **P0 (MVP Launch):** Essential for the Minimum Viable Product. These features validate the core concept and provide immediate user value.  
* **P1 (Fast Follow):** High-impact features to be developed immediately after the initial launch to increase engagement, differentiation, and user retention.  
* **P2 (Future Enhancement):** Important features for long-term success that can be developed after the core product is stable and validated.

### **1\. Core App Experience & Onboarding**

| Priority | Feature | Functionality Description |
| :---- | :---- | :---- |
| **P2** | **User Account Creation** | Simple email/password signup and Google/Apple sign-in options. |
| **P0** | **Onboarding Wizard** | A brief, skippable tutorial that explains the core concepts of <PERSON>'s Circles and the purpose of the app. It guides the user through adding their first few contacts. |
| **P0** | **Main Dashboard** | A clean, central screen showing "up next" reminders, a summary of recent activity, and access to core features. |
| **P0** | **Contact Import & Creation** | Ability to import contacts from the native phonebook with permission. Manual creation of new contacts. |
| **P0** | **Interaction Logging** | A simple form to log an interaction (e.g., call, text, meeting). Includes a notes field and a date picker. |
| **P1** | **Actionable Notifications** | Rich notifications that allow users to Connect, Log, or Snooze a reminder directly from the notification shade without opening the app. |
| **P2** | **Home Screen Widgets** | Android widgets to display upcoming reminders or a "contact of the day." |

### **2\. Relationship Management (Dunbar's Circles)**

| Priority | Feature | Functionality Description |
| :---- | :---- | :---- |
| **P0** | **Dunbar's Circles Implementation** | Pre-defined, non-editable circles: Support Clique (5), Sympathy Group (15), Active Network (50), Full Network (150). Users assign contacts to these circles. |
| **P0** | **Circle-Based Reminders** | Automatic scheduling of reminders based on the contact's circle. Default frequencies: Support (Weekly), Sympathy (Bi-Weekly), Active (Monthly), Full Network (Quarterly). |
| **P0** | **Contact Profile View** | A view for each contact showing their circle, contact info, reminder frequency, and a chronological history of all logged interactions and notes. |
| **P1** | **Customizable Reminder Frequency** | Allow users to override the default reminder frequency for individual contacts after they understand the core system. |
| **P2** | **Contact Tagging** | Ability to add custom tags to contacts (e.g., "Work," "University," "Hobby") for filtering and for use by the AI engine. |
| **P2** | **Relationship History Search** | Ability to search through all notes and logs for a specific contact to find key information. |

### **3\. Guided Journeys**

| Priority | Feature | Functionality Description |
| :---- | :---- | :---- |
| **P0** | **"Maintenance" Journey** | The default app mode. Functionality centers around the core loop of reminders and logging based on Dunbar's circles. |
| **P1** | **"Resurgence" Journey** | A step-by-step wizard to guide users through reconnecting with a contact from an "Archived" list. Includes prompts for crafting an initial message. |
| **P2** | **"Self-Care & Boundaries" Journey** | A mode that introduces a "Social Battery" tracker. When the battery is low, the app suggests snoozing non-critical reminders and provides tips for managing social energy. |

### **4\. AI "Catalyst Engine"**

| Priority | Feature | Functionality Description |
| :---- | :---- | :---- |
| **P1** | **Contextual Conversation Starters (Basic)** | In the contact detail view, a button to generate a simple, context-aware icebreaker based on the contact's tags and relationship type. |
| **P2** | **"Intelligent Memory" Prompts** | An AI feature that parses user notes to surface key details (e.g., names, dates, important events) in the reminder notification. This requires robust privacy controls and on-device processing. |
| **P2** | **Advanced Conversation Starters** | AI suggestions will incorporate information from recent notes and active "Journeys" to provide highly personalized and relevant prompts. |

### **5\. Gamification & Motivation**

| Priority | Feature | Functionality Description |
| :---- | :---- | :---- |
| **P1** | **Connection Streaks** | Visual indicators for maintaining consistent contact with individuals and with entire Dunbar circles (e.g., a flame icon with a number). |
| **P2** | **Achievement Badges** | A system for awarding badges for milestones (e.g., "Resurgence Complete," "3-Month Support Clique Streak"). |
| **P2** | **"Social Wellness" Dashboard** | A private dashboard with simple charts visualizing the user's activity over time, framed in positive, non-competitive language. |

### **6\. Monetization & Settings**

| Priority | Feature | Functionality Description |
| :---- | :---- | :---- |
| **P0** | **Free Tier** | Generous free usage. Limitations could be on the number of contacts in the two innermost circles (e.g., 5 in Support Clique, 10 in Sympathy Group) and access to only the "Maintenance" journey. |
| **P1** | **One-Time Pro Purchase** | A single in-app purchase that unlocks unlimited contacts in all circles, access to all "Journeys," and advanced features as they are released (e.g., AI Engine, Gamification Dashboard). |
| **P1** | **Data Export** | Allow users to export their data (contacts, notes, history) in a common format like CSV, ensuring they always control their information. |
| **P2** | **Calendar Integration** | Ability to sync reminders to the user's primary device calendar (e.g., Google Calendar). |

