// Dunbar's Circles configuration
export const DUNBAR_CIRCLES = {
  support: {
    name: 'Support Clique',
    description: 'Your closest relationships - family and best friends',
    limit: 5,
    frequency: 7, // days - Weekly
    color: '#FF6B6B', // Red
    icon: 'heart'
  },
  sympathy: {
    name: 'Sympathy Group',
    description: 'Close friends you turn to for support',
    limit: 15,
    frequency: 14, // days - Bi-weekly
    color: '#4ECDC4', // Teal
    icon: 'people'
  },
  active: {
    name: 'Active Network',
    description: 'Friends you see regularly and enjoy spending time with',
    limit: 50,
    frequency: 30, // days - Monthly
    color: '#45B7D1', // Blue
    icon: 'group'
  },
  full: {
    name: 'Full Network',
    description: 'Acquaintances and extended network',
    limit: 150,
    frequency: 90, // days - Quarterly
    color: '#96CEB4', // Green
    icon: 'contacts'
  }
};

// Interaction types for analytics
export const INTERACTION_TYPES = {
  call: {
    name: 'Phone Calls',
    icon: 'phone',
    description: 'Voice conversations',
    color: '#4CAF50'
  },
  text: {
    name: 'Text Messages',
    icon: 'message-text',
    description: 'Text and messaging apps',
    color: '#2196F3'
  },
  meeting: {
    name: 'In-Person',
    icon: 'account-group',
    description: 'Face-to-face meetings',
    color: '#FF9800'
  },
  other: {
    name: 'Other',
    icon: 'dots-horizontal',
    description: 'Other forms of contact',
    color: '#9E9E9E'
  }
};

export const FREE_TIER_LIMITS = {
  support: 5,
  sympathy: 10,
  active: 25,
  full: 50
};

export const CONTACT_CATEGORIES = {
  friend: {
    name: 'Friend',
    color: '#FF6B6B',
    icon: 'heart'
  },
  coworker: {
    name: 'Coworker',
    color: '#4ECDC4',
    icon: 'briefcase'
  },
  hobby: {
    name: 'Hobby',
    color: '#45B7D1',
    icon: 'gamepad-variant'
  },
  acquaintance: {
    name: 'Acquaintance',
    color: '#96CEB4',
    icon: 'account'
  }
};

// Export arrays for easy iteration
export const CIRCLE_TYPES = Object.keys(DUNBAR_CIRCLES);
export const CATEGORY_TYPES = Object.keys(CONTACT_CATEGORIES);
export const INTERACTION_TYPE_KEYS = Object.keys(INTERACTION_TYPES);
