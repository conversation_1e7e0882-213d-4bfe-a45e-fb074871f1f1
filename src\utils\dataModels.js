import { CIRCLE_TYPES, CATEGORY_TYPES } from '../constants/dunbarCircles';

/**
 * Contact data model
 */
export class Contact {
  constructor({
    id = null,
    name = '',
    phone = null,
    email = null,
    circle = 'full',
    categories = [],
    lastInteracted = null,
    recurringReminder = null,
    notes = '',
    createdAt = new Date(),
    updatedAt = new Date()
  } = {}) {
    this.id = id || this.generateId();
    this.name = name;
    this.phone = phone;
    this.email = email;
    this.circle = CIRCLE_TYPES.includes(circle) ? circle : 'full';
    this.categories = this.validateCategories(categories);
    this.lastInteracted = lastInteracted ? new Date(lastInteracted) : null;
    this.recurringReminder = recurringReminder; // ReminderRule object
    this.notes = notes;
    this.createdAt = new Date(createdAt);
    this.updatedAt = new Date(updatedAt);
  }

  validateCategories(categories) {
    if (!Array.isArray(categories)) return [];
    return categories.filter(category => CATEGORY_TYPES.includes(category));
  }

  generateId() {
    return 'contact_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      phone: this.phone,
      email: this.email,
      circle: this.circle,
      categories: this.categories,
      lastInteracted: this.lastInteracted?.toISOString(),
      recurringReminder: this.recurringReminder?.toJSON(),
      notes: this.notes,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString()
    };
  }

  static fromJSON(data) {
    const contact = new Contact(data);
    // Handle recurringReminder deserialization
    if (data.recurringReminder) {
      const { ReminderRule } = require('./dataModels');
      contact.recurringReminder = ReminderRule.fromJSON(data.recurringReminder);
    }
    return contact;
  }
}

/**
 * Interaction data model
 */
export class Interaction {
  constructor({
    id = null,
    contactId = '',
    type = 'other',
    date = new Date(),
    duration = null, // in minutes for calls/meetings
    quality = 'good', // 'brief', 'good', 'deep'
    mood = 'positive', // 'positive', 'neutral', 'challenging'
    notes = '',
    createdAt = new Date()
  } = {}) {
    this.id = id || this.generateId();
    this.contactId = contactId;
    this.type = ['call', 'text', 'meeting', 'other'].includes(type) ? type : 'other';
    this.date = new Date(date);
    this.duration = duration && duration > 0 ? duration : null;
    this.quality = ['brief', 'good', 'deep'].includes(quality) ? quality : 'good';
    this.mood = ['positive', 'neutral', 'challenging'].includes(mood) ? mood : 'positive';
    this.notes = notes;
    this.createdAt = new Date(createdAt);
  }

  generateId() {
    return 'interaction_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  toJSON() {
    return {
      id: this.id,
      contactId: this.contactId,
      type: this.type,
      date: this.date.toISOString(),
      duration: this.duration,
      quality: this.quality,
      mood: this.mood,
      notes: this.notes,
      createdAt: this.createdAt.toISOString()
    };
  }

  static fromJSON(data) {
    return new Interaction(data);
  }
}

/**
 * Reminder Rule data model (stored within contact)
 * Defines recurring reminder frequency for a contact
 */
export class ReminderRule {
  constructor({
    id = null,
    contactId = '',
    type = 'recurring',
    frequency = 30, // Days between reminders
    updatedDate = new Date()
  } = {}) {
    this.id = id || this.generateId();
    this.contactId = contactId;
    this.type = 'recurring';
    this.frequency = frequency > 0 ? frequency : 30;
    this.updatedDate = new Date(updatedDate);
  }

  generateId() {
    return 'rule_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  toJSON() {
    return {
      id: this.id,
      contactId: this.contactId,
      type: this.type,
      frequency: this.frequency,
      updatedDate: this.updatedDate.toISOString()
    };
  }

  static fromJSON(data) {
    return new ReminderRule(data);
  }
}

/**
 * Reminder Instance data model (stored in database)
 * Represents actual reminder occurrences with completion and snooze tracking
 */
export class ReminderInstance {
  constructor({
    id = null,
    contactId = '',
    reminderDate = new Date(),
    state = 'PENDING',
    completedDate = null,
    snoozedUntilDate = null,
    reminderType = 'RECURRING',
    reminderRuleId = null,
    createdAt = new Date(),
    updatedAt = new Date()
  } = {}) {
    this.id = id || this.generateId();
    this.contactId = contactId;
    this.reminderDate = new Date(reminderDate);
    this.state = ['PENDING', 'COMPLETED', 'CANCELLED'].includes(state) ? state : 'PENDING';
    this.completedDate = completedDate ? new Date(completedDate) : null;
    this.snoozedUntilDate = snoozedUntilDate ? new Date(snoozedUntilDate) : null;
    this.reminderType = ['RECURRING', 'ONCE'].includes(reminderType) ? reminderType : 'RECURRING';
    this.reminderRuleId = reminderRuleId;
    this.createdAt = new Date(createdAt);
    this.updatedAt = new Date(updatedAt);
  }

  generateId() {
    return 'instance_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Helper methods
  get isSnoozed() {
    return this.snoozedUntilDate !== null && this.snoozedUntilDate > new Date();
  }

  get isOverdue() {
    if (this.state === 'COMPLETED') return false;
    return this.reminderDate < new Date();
  }

  get isUpcoming() {
    if (this.state === 'COMPLETED') return false;
    return this.reminderDate >= new Date();
  }

  get isActive() {
    return this.state === 'PENDING' && !this.isSnoozed;
  }

  // Mark as completed
  markCompleted(completedDate = new Date()) {
    this.state = 'COMPLETED';
    this.completedDate = new Date(completedDate);
    this.updatedAt = new Date();
  }

  // Mark as cancelled
  markCancelled() {
    this.state = 'CANCELLED';
    this.updatedAt = new Date();
  }

  // Snooze until a specific date
  snoozeUntil(snoozedUntilDate) {
    this.snoozedUntilDate = new Date(snoozedUntilDate);
    this.updatedAt = new Date();
  }

  // Remove snooze
  unsnooze() {
    this.snoozedUntilDate = null;
    this.updatedAt = new Date();
  }

  toJSON() {
    return {
      id: this.id,
      contactId: this.contactId,
      reminderDate: this.reminderDate.toISOString(),
      state: this.state,
      completedDate: this.completedDate?.toISOString(),
      snoozedUntilDate: this.snoozedUntilDate?.toISOString(),
      reminderType: this.reminderType,
      reminderRuleId: this.reminderRuleId,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString()
    };
  }

  static fromJSON(data) {
    return new ReminderInstance({
      ...data,
      reminderDate: new Date(data.reminderDate),
      state: data.state || 'PENDING', // Ensure backward compatibility
      completedDate: data.completedDate ? new Date(data.completedDate) : null,
      snoozedUntilDate: data.snoozedUntilDate ? new Date(data.snoozedUntilDate) : null,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt)
    });
  }
}




