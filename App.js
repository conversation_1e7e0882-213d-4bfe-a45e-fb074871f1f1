import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { useColorScheme } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import {
  PaperProvider,
  MD3LightTheme,
  MD3DarkTheme,
  adaptNavigationTheme
} from 'react-native-paper';
import {
  NavigationContainer,
  DefaultTheme as NavigationDefaultTheme,
  DarkTheme as NavigationDarkTheme,
} from '@react-navigation/native';
import { AppProvider } from './src/context/AppContext';
import AppNavigator from './src/navigation/AppNavigator';

// Custom Material Design themes
const customLightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#4ECDC4',
    primaryContainer: '#E0F7F5',
    secondary: '#45B7D1',
    secondaryContainer: '#E3F2FD',
    tertiary: '#FF6B6B',
    tertiaryContainer: '#FFEBEE',
    surface: '#FFFFFF',
    surfaceVariant: '#F5F5F5',
    background: '#FAFAFA',
  },
};

const customDarkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#4ECDC4',
    primaryContainer: '#1A4A47',
    secondary: '#45B7D1',
    secondaryContainer: '#1A3A4A',
    tertiary: '#FF6B6B',
    tertiaryContainer: '#4A1A1A',
    surface: '#1E1E1E',
    surfaceVariant: '#2A2A2A',
    background: '#121212',
  },
};

// Adapt navigation themes to match Paper themes
const { LightTheme: AdaptedLightTheme, DarkTheme: AdaptedDarkTheme } = adaptNavigationTheme({
  reactNavigationLight: NavigationDefaultTheme,
  reactNavigationDark: NavigationDarkTheme,
  materialLight: customLightTheme,
  materialDark: customDarkTheme,
});

export default function App() {
  const colorScheme = useColorScheme();

  // Choose theme based on system preference
  const paperTheme = colorScheme === 'dark' ? customDarkTheme : customLightTheme;
  const navigationTheme = colorScheme === 'dark' ? AdaptedDarkTheme : AdaptedLightTheme;

  return (
    <SafeAreaProvider>
      <PaperProvider theme={paperTheme}>
        <AppProvider>
          <AppNavigator navigationTheme={navigationTheme} />
          <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
        </AppProvider>
      </PaperProvider>
    </SafeAreaProvider>
  );
}
