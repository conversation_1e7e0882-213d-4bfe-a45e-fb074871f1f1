/**
 * Relationship Overview Analytics
 * Provides high-level metrics about relationship maintenance
 */

// Helper function to get days between dates
const getDaysBetween = (date1, date2) => {
  const diffTime = Math.abs(date2 - date1);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Helper function to get start of period
const getStartOfPeriod = (period = 'week') => {
  const now = new Date();
  switch (period) {
    case 'week':
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      startOfWeek.setHours(0, 0, 0, 0);
      return startOfWeek;
    case 'month':
      return new Date(now.getFullYear(), now.getMonth(), 1);
    default:
      return new Date(now.getFullYear(), now.getMonth(), 1);
  }
};

/**
 * Get total contact count
 * @param {Array} contacts - Array of contacts
 * @returns {number} Total number of contacts
 */
export const getContactCount = (contacts) => {
  return contacts.length;
};

/**
 * Get weekly interactions count
 * @param {Array} interactions - Array of interactions
 * @returns {number} Number of interactions in the last 7 days
 */
export const getWeeklyInteractions = (interactions) => {
  const startOfWeek = getStartOfPeriod('week');
  return interactions.filter(interaction => 
    new Date(interaction.date) >= startOfWeek
  ).length;
};

/**
 * Get monthly interactions count
 * @param {Array} interactions - Array of interactions
 * @returns {number} Number of interactions in the last 30 days
 */
export const getMonthlyInteractions = (interactions) => {
  const startOfMonth = getStartOfPeriod('month');
  return interactions.filter(interaction => 
    new Date(interaction.date) >= startOfMonth
  ).length;
};

/**
 * Get overall relationship maintenance score
 * @param {Array} contacts - Array of contacts
 * @param {Array} interactions - Array of interactions
 * @returns {number} Overall maintenance score (0-100%)
 */
export const getOverallOnTrackScore = (contacts, interactions) => {
  if (contacts.length === 0) return 100;

  const { analyzeInteractionPatterns } = require('./interactionAnalytics');
  const analytics = analyzeInteractionPatterns(contacts, interactions);
  
  return Math.round(analytics.frequencyAnalysis.overallOnTrackPercentage);
};

/**
 * Get trend direction for interactions
 * @param {Array} interactions - Array of interactions
 * @returns {string} Trend direction: 'up', 'down', or 'stable'
 */
export const getInteractionTrend = (interactions) => {
  const now = new Date();
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

  const thisWeek = interactions.filter(i => 
    new Date(i.date) >= oneWeekAgo && new Date(i.date) <= now
  ).length;

  const lastWeek = interactions.filter(i => 
    new Date(i.date) >= twoWeeksAgo && new Date(i.date) < oneWeekAgo
  ).length;

  if (thisWeek > lastWeek) return 'up';
  if (thisWeek < lastWeek) return 'down';
  return 'stable';
};

/**
 * Get comprehensive relationship overview
 * @param {Array} contacts - Array of contacts
 * @param {Array} interactions - Array of interactions
 * @returns {Object} Complete relationship overview metrics
 */
export const getRelationshipOverview = (contacts, interactions) => {
  return {
    totalContacts: getContactCount(contacts),
    weeklyInteractions: getWeeklyInteractions(interactions),
    monthlyInteractions: getMonthlyInteractions(interactions),
    onTrackScore: getOverallOnTrackScore(contacts, interactions),
    trend: getInteractionTrend(interactions)
  };
};
