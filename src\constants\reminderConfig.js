/**
 * Reminder Configuration Constants
 * Centralized configuration for reminder behavior and display
 */

export const REMINDER_CONFIG = {
  // Number of days ahead to show upcoming reminders on home page
  HOME_PREVIEW_DAYS: 5,
  
  // Snooze duration options (in days)
  SNOOZE_DURATIONS: {
    '1day': 1,
    '1week': 7,
    '1month': 30,
    '1year': 365,
    'indefinite': 365 * 10 // 10 years
  },
  
  // Auto-generation settings
  AUTO_GENERATE: {
    // Generate reminder instances on app startup
    ON_STARTUP: true,

    // Generate reminder instances when contacts are added
    ON_CONTACT_ADD: true,

    // Update reminder instances when contacts are updated (circle change, etc.)
    ON_CONTACT_UPDATE: true,

    // Clean up reminder instances when contacts are deleted
    ON_CONTACT_DELETE: true,

    // Generate reminder instances when interactions are logged
    ON_INTERACTION_LOG: true,

    // Update reminder instances when recurring reminder rules change
    ON_REMINDER_RULE_CHANGE: true
  },
  
  // Display settings
  DISPLAY: {
    // Maximum number of reminders to show on home page
    MAX_HOME_REMINDERS: 10,
    
    // Show overdue reminders first
    PRIORITIZE_OVERDUE: true,
    
    // Show reminder count in section headers
    SHOW_COUNTS: true
  }
};

export default REMINDER_CONFIG;
