import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import {
  Card,
  Text,
  Surface,
  IconButton,
  Divider,
  Button,
  TouchableRipple
} from 'react-native-paper';
import { INTERACTION_TYPES } from '../constants/dunbarCircles';

export default function InteractionHistory({ interactions, contactName, onLogInteraction, navigation }) {

  // Render individual interaction item
  const renderInteractionItem = ({ item, index }) => {
    const interactionType = INTERACTION_TYPES[item.type];
    const interactionDate = new Date(item.date);
    const now = new Date();
    const daysDiff = Math.ceil((now - interactionDate) / (1000 * 60 * 60 * 24));

    // Format relative date
    const getRelativeDate = () => {
      if (daysDiff === 0) return 'Today';
      if (daysDiff === 1) return 'Yesterday';
      if (daysDiff < 7) return `${daysDiff} days ago`;
      if (daysDiff < 30) {
        const weeks = Math.floor(daysDiff / 7);
        return `${weeks} week${weeks === 1 ? '' : 's'} ago`;
      }
      if (daysDiff < 365) {
        const months = Math.floor(daysDiff / 30);
        return `${months} month${months === 1 ? '' : 's'} ago`;
      }
      const years = Math.floor(daysDiff / 365);
      return `${years} year${years === 1 ? '' : 's'} ago`;
    };

    // Get mood color
    const getMoodColor = () => {
      switch (item.mood) {
        case 'positive': return '#4CAF50';
        case 'neutral': return '#FF9800';
        case 'challenging': return '#FF6B6B';
        default: return '#9E9E9E';
      }
    };

    // Get quality indicator
    const getQualityText = () => {
      switch (item.quality) {
        case 'brief': return 'Brief';
        case 'good': return 'Good';
        case 'deep': return 'Deep';
        default: return '';
      }
    };

    const handleInteractionPress = () => {
      if (navigation) {
        navigation.navigate('LogDetail', {
          interaction: item,
          contact: { name: contactName } // Pass contact info for display
        });
      }
    };

    return (
      <View style={styles.interactionItem}>
        <TouchableRipple onPress={handleInteractionPress} disabled={!navigation}>
          <View style={styles.interactionContent}>
            <View style={styles.interactionHeader}>
              <View style={styles.interactionTypeContainer}>
                <Surface
                  style={[styles.typeIconContainer, { backgroundColor: interactionType.color + '20' }]}
                  elevation={1}
                >
                  <IconButton
                    icon={interactionType.icon}
                    size={20}
                    iconColor={interactionType.color}
                    style={styles.typeIcon}
                  />
                </Surface>
                <View style={styles.interactionInfo}>
                  <Text variant="bodyLarge" style={styles.interactionTypeName}>
                    {interactionType.name}
                  </Text>
                  <Text variant="bodySmall" style={styles.interactionDate}>
                    {getRelativeDate()} • {interactionDate.toLocaleDateString()}
                  </Text>
                </View>
              </View>

              <View style={styles.interactionMeta}>
                {item.duration && (
                  <Text variant="bodySmall" style={styles.durationText}>
                    {item.duration} min
                  </Text>
                )}
                <Surface
                  style={[styles.moodIndicator, { backgroundColor: getMoodColor() }]}
                  elevation={1}
                />
                {navigation && <IconButton icon="chevron-right" size={16} style={styles.chevronIcon} />}
              </View>
            </View>

            {(item.quality !== 'good' || item.notes) && (
              <View style={styles.interactionDetails}>
                {item.quality !== 'good' && (
                  <Text variant="bodySmall" style={styles.qualityText}>
                    Quality: {getQualityText()}
                  </Text>
                )}
                {item.notes && (
                  <Text variant="bodyMedium" style={styles.notesText}>
                    {item.notes}
                  </Text>
                )}
              </View>
            )}
          </View>
        </TouchableRipple>

        {index < interactions.length - 1 && <Divider style={styles.divider} />}
      </View>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Surface style={styles.emptyIconContainer} elevation={1}>
        <IconButton
          icon="message-outline"
          size={48}
          iconColor="#ccc"
        />
      </Surface>
      <Text variant="titleMedium" style={styles.emptyTitle}>
        No Interactions Yet
      </Text>
      <Text variant="bodyMedium" style={styles.emptySubtitle}>
        Start building your relationship with {contactName} by logging your first interaction.
      </Text>
      <Button
        mode="contained"
        icon="plus"
        onPress={onLogInteraction}
        style={styles.emptyStateButton}
      >
        Log Interaction
      </Button>
    </View>
  );

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.headerText}>
              <Text variant="titleLarge" style={styles.title}>
                Interaction History
              </Text>
              <Text variant="bodyMedium" style={styles.subtitle}>
                {interactions.length} interaction{interactions.length === 1 ? '' : 's'}
              </Text>
            </View>
            <Button
              mode="outlined"
              icon="plus"
              onPress={onLogInteraction}
              style={styles.headerButton}
              compact
            >
              Log Interaction
            </Button>
          </View>
        </View>

        {interactions.length === 0 ? (
          renderEmptyState()
        ) : (
          <FlatList
            data={interactions}
            renderItem={renderInteractionItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        )}
      </Card.Content>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  header: {
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    opacity: 0.7,
  },
  headerButton: {
    marginLeft: 16,
  },
  interactionItem: {
    paddingVertical: 8,
  },
  interactionContent: {
    paddingHorizontal: 0,
  },
  interactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  interactionTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  typeIconContainer: {
    borderRadius: 16,
    marginRight: 12,
  },
  typeIcon: {
    margin: 0,
  },
  interactionInfo: {
    flex: 1,
  },
  interactionTypeName: {
    fontWeight: '600',
    marginBottom: 2,
  },
  interactionDate: {
    opacity: 0.7,
  },
  interactionMeta: {
    alignItems: 'flex-end',
    gap: 4,
  },
  durationText: {
    opacity: 0.7,
    fontWeight: '500',
  },
  moodIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  chevronIcon: {
    margin: 0,
    marginLeft: 8,
  },
  interactionDetails: {
    marginLeft: 44,
    gap: 4,
  },
  qualityText: {
    opacity: 0.7,
    fontStyle: 'italic',
  },
  notesText: {
    lineHeight: 18,
    opacity: 0.8,
  },
  divider: {
    marginTop: 12,
    marginLeft: 44,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyIconContainer: {
    borderRadius: 50,
    marginBottom: 16,
  },
  emptyTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  emptyStateButton: {
    marginTop: 8,
  },
});
