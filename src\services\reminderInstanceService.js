/**
 * Reminder Instance Service
 * Handles automatic generation and management of reminder instances
 */

import { ReminderInstance } from '../utils/dataModels';
import { DUNBAR_CIRCLES } from '../constants/dunbarCircles';
import { REMINDER_CONFIG } from '../constants/reminderConfig';

/**
 * Add days to a date
 * @param {Date} date - Base date
 * @param {number} days - Number of days to add
 * @returns {Date} New date with days added
 */
const addDays = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

/**
 * Get the most recent interaction date for a contact
 * @param {string} contactId - Contact ID
 * @param {Array} interactions - Array of interactions
 * @returns {Date|null} Most recent interaction date or null
 */
const getLastInteractionDate = (contactId, interactions) => {
  const contactInteractions = interactions.filter(i => i.contactId === contactId);
  if (contactInteractions.length === 0) return null;
  
  const sortedInteractions = contactInteractions.sort((a, b) => new Date(b.date) - new Date(a.date));
  return new Date(sortedInteractions[0].date);
};

/**
 * Generate initial reminder instance for a new contact
 * @param {Contact} contact - Contact object
 * @param {Array} interactions - Array of interactions (usually empty for new contacts)
 * @returns {ReminderInstance|null} Generated reminder instance or null
 */
export const generateInitialReminderInstance = (contact, interactions = []) => {
  try {
    if (!contact.recurringReminder) {
      console.log(`Contact ${contact.name} has no recurring reminder rule`);
      return null;
    }

    const circle = DUNBAR_CIRCLES[contact.circle];
    if (!circle) {
      console.warn(`Unknown circle for contact ${contact.name}: ${contact.circle}`);
      return null;
    }

    const frequency = contact.recurringReminder.frequency;
    const lastInteractionDate = getLastInteractionDate(contact.id, interactions);
    
    // Calculate initial reminder date
    let reminderDate;
    if (lastInteractionDate) {
      // If there are interactions, base on last interaction
      reminderDate = addDays(lastInteractionDate, frequency);
    } else {
      // For new contacts, base on creation date
      reminderDate = addDays(new Date(contact.createdAt), frequency);
    }

    const instance = new ReminderInstance({
      contactId: contact.id,
      reminderDate: reminderDate,
      reminderType: 'RECURRING',
      reminderRuleId: contact.recurringReminder.id
    });

    console.log(`Generated initial reminder instance for ${contact.name}, due: ${instance.reminderDate.toISOString()}`);
    return instance;
  } catch (error) {
    console.error(`Error generating initial reminder instance for contact ${contact.name}:`, error);
    return null;
  }
};

/**
 * Generate next reminder instance after completing current one
 * @param {Contact} contact - Contact object
 * @param {Date} completionDate - Date when current reminder was completed
 * @returns {ReminderInstance|null} Generated next reminder instance or null
 */
export const generateNextReminderInstance = (contact, completionDate = new Date()) => {
  try {
    if (!contact.recurringReminder) {
      console.log(`Contact ${contact.name} has no recurring reminder rule`);
      return null;
    }

    const frequency = contact.recurringReminder.frequency;
    const reminderDate = addDays(new Date(completionDate), frequency);

    const instance = new ReminderInstance({
      contactId: contact.id,
      reminderDate: reminderDate,
      reminderType: 'RECURRING',
      reminderRuleId: contact.recurringReminder.id
    });

    console.log(`Generated next reminder instance for ${contact.name}, due: ${instance.reminderDate.toISOString()}`);
    return instance;
  } catch (error) {
    console.error(`Error generating next reminder instance for contact ${contact.name}:`, error);
    return null;
  }
};

/**
 * Update pending reminder instance when contact circle changes
 * @param {Contact} updatedContact - Updated contact object
 * @param {Contact} previousContact - Previous contact object
 * @param {Array} interactions - Array of interactions
 * @param {Array} reminderInstances - Array of existing reminder instances
 * @returns {Object} Object with instances to update, add, or delete
 */
export const syncContactReminderInstances = (updatedContact, previousContact, interactions, reminderInstances) => {
  const result = {
    instancesToAdd: [],
    instancesToUpdate: [],
    instancesToDelete: []
  };

  try {
    // Find pending instances for this contact
    const pendingInstances = reminderInstances.filter(
      i => i.contactId === updatedContact.id && i.state === 'PENDING'
    );

    // Check if circle changed
    const circleChanged = updatedContact.circle !== previousContact.circle;
    
    if (circleChanged && updatedContact.recurringReminder) {
      console.log(`Circle changed for ${updatedContact.name}: ${previousContact.circle} -> ${updatedContact.circle}`);
      console.log(`Previous frequency: ${previousContact.recurringReminder?.frequency}, New frequency: ${updatedContact.recurringReminder.frequency}`);
      console.log(`Found ${pendingInstances.length} pending instances to update`);

      const newFrequency = updatedContact.recurringReminder.frequency;
      const lastInteractionDate = getLastInteractionDate(updatedContact.id, interactions);

      // Calculate new reminder date using last interaction or contact creation date
      let baseDate;
      if (lastInteractionDate) {
        baseDate = lastInteractionDate;
        console.log(`Using last interaction date as base: ${baseDate}`);
      } else {
        baseDate = new Date(updatedContact.createdAt);
        console.log(`No interactions found, using contact creation date as base: ${baseDate}`);
      }

      const newReminderDate = addDays(baseDate, newFrequency);
      console.log(`Calculated new reminder date: ${newReminderDate} (base + ${newFrequency} days)`);

      if (pendingInstances.length > 0) {
        // Update existing pending instance
        const instanceToUpdate = pendingInstances[0];
        console.log(`Updating pending instance: ${instanceToUpdate.reminderDate} -> ${newReminderDate}`);
        instanceToUpdate.reminderDate = newReminderDate;
        instanceToUpdate.reminderRuleId = updatedContact.recurringReminder.id;
        instanceToUpdate.updatedAt = new Date();

        result.instancesToUpdate.push(instanceToUpdate);
        
        // Delete any additional pending instances (should only be one)
        if (pendingInstances.length > 1) {
          result.instancesToDelete.push(...pendingInstances.slice(1));
        }
      } else {
        // Create new pending instance
        const newInstance = new ReminderInstance({
          contactId: updatedContact.id,
          reminderDate: newReminderDate,
          reminderType: 'RECURRING',
          reminderRuleId: updatedContact.recurringReminder.id
        });
        
        result.instancesToAdd.push(newInstance);
      }
    } else if (!updatedContact.recurringReminder && pendingInstances.length > 0) {
      // Contact no longer has recurring reminder, delete pending instances
      result.instancesToDelete.push(...pendingInstances);
    } else if (updatedContact.recurringReminder && pendingInstances.length === 0) {
      // Contact now has recurring reminder but no pending instances, create one
      const newInstance = generateInitialReminderInstance(updatedContact, interactions);
      if (newInstance) {
        result.instancesToAdd.push(newInstance);
      }
    }

    return result;
  } catch (error) {
    console.error(`Error syncing reminder instances for contact ${updatedContact.name}:`, error);
    return result;
  }
};

/**
 * Handle interaction logging - complete current instance and generate next
 * @param {Contact} contact - Contact object
 * @param {Interaction} interaction - Logged interaction
 * @param {Array} reminderInstances - Array of existing reminder instances
 * @returns {Object} Object with instances to update and add
 */
export const handleInteractionLogged = (contact, interaction, reminderInstances) => {
  const result = {
    instancesToUpdate: [],
    instancesToAdd: []
  };

  try {
    if (!contact.recurringReminder) {
      console.log(`Contact ${contact.name} has no recurring reminder rule`);
      return result;
    }

    // Find pending instances for this contact
    const pendingInstances = reminderInstances.filter(
      i => i.contactId === contact.id && i.state === 'PENDING'
    );

    // Complete the current pending instance if it exists
    if (pendingInstances.length > 0) {
      const currentInstance = pendingInstances[0];
      currentInstance.markCompleted(new Date(interaction.date));
      result.instancesToUpdate.push(currentInstance);
      
      console.log(`Completed reminder instance for ${contact.name} due to interaction on ${interaction.date}`);
    }

    // Generate next reminder instance
    const nextInstance = generateNextReminderInstance(contact, new Date(interaction.date));
    if (nextInstance) {
      result.instancesToAdd.push(nextInstance);
    }

    return result;
  } catch (error) {
    console.error(`Error handling interaction logged for contact ${contact.name}:`, error);
    return result;
  }
};

/**
 * Clean up reminder instances when contact is deleted
 * @param {string} contactId - Contact ID
 * @param {Array} reminderInstances - Array of existing reminder instances
 * @returns {Array} Array of instances to delete
 */
export const cleanupContactReminderInstances = (contactId, reminderInstances) => {
  try {
    const instancesToDelete = reminderInstances.filter(i => i.contactId === contactId);
    console.log(`Found ${instancesToDelete.length} reminder instances to delete for contact ${contactId}`);
    return instancesToDelete;
  } catch (error) {
    console.error(`Error cleaning up reminder instances for contact ${contactId}:`, error);
    return [];
  }
};

/**
 * Complete a reminder instance and generate next one if recurring
 * @param {ReminderInstance} instance - Instance to complete
 * @param {Contact} contact - Contact object
 * @param {Date} completionDate - Date of completion
 * @returns {Object} Object with instance to update and potentially add
 */
export const completeReminderInstance = (instance, contact, completionDate = new Date()) => {
  const result = {
    instanceToUpdate: null,
    instanceToAdd: null
  };

  try {
    // Mark current instance as completed
    instance.markCompleted(completionDate);
    result.instanceToUpdate = instance;

    // Generate next instance if this is a recurring reminder
    if (instance.reminderType === 'RECURRING' && contact.recurringReminder) {
      const nextInstance = generateNextReminderInstance(contact, completionDate);
      if (nextInstance) {
        result.instanceToAdd = nextInstance;
      }
    }

    console.log(`Completed reminder instance for contact ${contact.name}`);
    return result;
  } catch (error) {
    console.error(`Error completing reminder instance:`, error);
    return result;
  }
};

/**
 * Generate reminder instances for all contacts that don't have pending instances
 * Used during app startup or migration
 * @param {Array} contacts - Array of contacts
 * @param {Array} interactions - Array of interactions
 * @param {Array} existingInstances - Array of existing reminder instances
 * @returns {Array} Array of new instances to create
 */
export const generateMissingReminderInstances = (contacts, interactions, existingInstances = []) => {
  const newInstances = [];

  try {
    for (const contact of contacts) {
      if (!contact.recurringReminder) continue;

      // Check if contact already has a pending instance
      const hasPendingInstance = existingInstances.some(
        i => i.contactId === contact.id && i.state === 'PENDING'
      );

      if (!hasPendingInstance) {
        const instance = generateInitialReminderInstance(contact, interactions);
        if (instance) {
          newInstances.push(instance);
        }
      }
    }

    console.log(`Generated ${newInstances.length} missing reminder instances`);
    return newInstances;
  } catch (error) {
    console.error('Error generating missing reminder instances:', error);
    return [];
  }
};
