import React, { useMemo } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { 
  Card, 
  Text, 
  Surface,
  ProgressBar,
  Chip,
  useTheme 
} from 'react-native-paper';
import { analyzeInteractionPatterns } from '../utils/interactionAnalytics';
import { DUNBAR_CIRCLES, INTERACTION_TYPES } from '../constants/dunbarCircles';

const { width: screenWidth } = Dimensions.get('window');

export default function InteractionAnalytics({ contacts, interactions }) {
  const theme = useTheme();

  // Calculate analytics
  const analytics = useMemo(() => {
    return analyzeInteractionPatterns(contacts, interactions);
  }, [contacts, interactions]);

  // Render summary stats
  const renderSummaryStats = () => (
    <Card style={styles.summaryCard}>
      <Card.Content>
        <Text variant="titleLarge" style={styles.title}>
          Your Communication Patterns
        </Text>
        
        <View style={styles.summaryGrid}>
          <Surface style={styles.summaryItem} elevation={1}>
            <Text variant="headlineMedium" style={styles.summaryNumber}>
              {analytics.summary.monthlyInteractions}
            </Text>
            <Text variant="bodyMedium" style={styles.summaryLabel}>
              This Month
            </Text>
          </Surface>
          
          <Surface style={styles.summaryItem} elevation={1}>
            <Text variant="headlineMedium" style={styles.summaryNumber}>
              {analytics.summary.weeklyInteractions}
            </Text>
            <Text variant="bodyMedium" style={styles.summaryLabel}>
              This Week
            </Text>
          </Surface>
          
          <Surface style={styles.summaryItem} elevation={1}>
            <Text variant="headlineMedium" style={styles.summaryNumber}>
              {Math.round(analytics.frequencyAnalysis.overallOnTrackPercentage)}%
            </Text>
            <Text variant="bodyMedium" style={styles.summaryLabel}>
              On Track
            </Text>
          </Surface>
        </View>
      </Card.Content>
    </Card>
  );

  // Render circle maintenance scores
  const renderCircleMaintenance = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.cardTitle}>
          Circle Maintenance
        </Text>
        <Text variant="bodySmall" style={styles.cardSubtitle}>
          How well you're staying connected with each circle
        </Text>
        
        <View style={styles.circleMaintenanceContainer}>
          {Object.keys(analytics.circleAnalysis).map(circleKey => {
            const circle = analytics.circleAnalysis[circleKey];
            const score = circle.maintenanceScore;
            const scoreColor = getScoreColor(score);
            
            return (
              <View key={circleKey} style={styles.circleMaintenanceItem}>
                <View style={styles.circleMaintenanceHeader}>
                  <View style={[styles.circleIndicator, { backgroundColor: circle.color }]} />
                  <View style={styles.circleMaintenanceInfo}>
                    <Text variant="bodyMedium" style={styles.circleName}>
                      {circle.name}
                    </Text>
                    <Text variant="bodySmall" style={styles.circleStats}>
                      {circle.monthlyInteractions} interactions this month
                    </Text>
                  </View>
                  <Text variant="titleMedium" style={[styles.scoreText, { color: scoreColor }]}>
                    {score}%
                  </Text>
                </View>
                <ProgressBar 
                  progress={score / 100} 
                  color={scoreColor}
                  style={styles.maintenanceProgressBar}
                />
              </View>
            );
          })}
        </View>
      </Card.Content>
    </Card>
  );

  // Render interaction type preferences
  const renderInteractionTypes = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.cardTitle}>
          Your Communication Style
        </Text>
        <Text variant="bodySmall" style={styles.cardSubtitle}>
          How you prefer to stay in touch
        </Text>
        
        <View style={styles.typeAnalysisContainer}>
          {Object.keys(analytics.typeAnalysis.typeCounts).map(typeKey => {
            const typeData = analytics.typeAnalysis.typeCounts[typeKey];
            const isPreferred = typeKey === analytics.typeAnalysis.preferredType;
            
            return (
              <View key={typeKey} style={styles.typeItem}>
                <View style={styles.typeHeader}>
                  <Text variant="bodyMedium" style={styles.typeName}>
                    {typeData.name}
                  </Text>
                  <Text variant="bodySmall" style={styles.typePercentage}>
                    {Math.round(typeData.percentage)}%
                  </Text>
                </View>
                <ProgressBar 
                  progress={typeData.percentage / 100} 
                  color={isPreferred ? theme.colors.primary : theme.colors.outline}
                  style={styles.typeProgressBar}
                />
                {isPreferred && (
                  <Chip 
                    mode="flat" 
                    style={styles.preferredChip}
                    textStyle={styles.preferredChipText}
                  >
                    Preferred
                  </Chip>
                )}
              </View>
            );
          })}
        </View>
      </Card.Content>
    </Card>
  );

  // Render trend analysis
  const renderTrendAnalysis = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.cardTitle}>
          Recent Trends
        </Text>
        <Text variant="bodySmall" style={styles.cardSubtitle}>
          Your communication activity over the last 6 months
        </Text>
        
        <View style={styles.trendContainer}>
          <View style={styles.trendChart}>
            {analytics.trendAnalysis.monthlyData.map((month, index) => {
              const maxCount = Math.max(...analytics.trendAnalysis.monthlyData.map(m => m.count));
              const height = maxCount > 0 ? (month.count / maxCount) * 60 : 0;
              
              return (
                <View key={index} style={styles.trendBar}>
                  <View 
                    style={[
                      styles.trendBarFill, 
                      { 
                        height: height,
                        backgroundColor: theme.colors.primary 
                      }
                    ]} 
                  />
                  <Text variant="bodySmall" style={styles.trendBarLabel}>
                    {month.month.split(' ')[0]}
                  </Text>
                  <Text variant="bodySmall" style={styles.trendBarCount}>
                    {month.count}
                  </Text>
                </View>
              );
            })}
          </View>
          
          <View style={styles.trendInsight}>
            <Text variant="bodyMedium" style={styles.trendInsightText}>
              {getTrendMessage(analytics.trendAnalysis.trendDirection)}
            </Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  // Render insights
  const renderInsights = () => {
    if (analytics.insights.length === 0) return null;

    return (
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.cardTitle}>
            Insights & Suggestions
          </Text>
          
          <View style={styles.insightsContainer}>
            {analytics.insights.map((insight, index) => (
              <Surface key={index} style={styles.insightItem} elevation={1}>
                <Text variant="bodyMedium" style={styles.insightTitle}>
                  {insight.title}
                </Text>
                <Text variant="bodySmall" style={styles.insightMessage}>
                  {insight.message}
                </Text>
                {insight.suggestion && (
                  <Text variant="bodySmall" style={styles.insightSuggestion}>
                    💡 {insight.suggestion}
                  </Text>
                )}
              </Surface>
            ))}
          </View>
        </Card.Content>
      </Card>
    );
  };

  // Helper functions
  const getScoreColor = (score) => {
    if (score >= 80) return '#4CAF50'; // Green
    if (score >= 60) return '#FF9800'; // Orange
    return '#F44336'; // Red
  };

  const getTrendMessage = (direction) => {
    switch (direction) {
      case 'improving':
        return '📈 You\'ve been more active lately! Keep it up.';
      case 'declining':
        return '📉 Consider reaching out more often to maintain connections.';
      default:
        return '📊 Your communication has been consistent.';
    }
  };

  return (
    <View style={styles.container}>
      {renderSummaryStats()}
      {renderCircleMaintenance()}
      {renderInteractionTypes()}
      {renderTrendAnalysis()}
      {renderInsights()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    // Remove padding to match Dashboard card width
    gap: 16,
  },
  summaryCard: {
    elevation: 2,
  },
  card: {
    elevation: 2,
  },
  title: {
    marginBottom: 16,
    fontWeight: 'bold',
  },
  cardTitle: {
    marginBottom: 4,
    fontWeight: 'bold',
  },
  cardSubtitle: {
    marginBottom: 16,
    opacity: 0.7,
  },
  summaryGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  summaryItem: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    borderRadius: 8,
  },
  summaryNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summaryLabel: {
    opacity: 0.7,
    textAlign: 'center',
  },
  circleMaintenanceContainer: {
    gap: 16,
  },
  circleMaintenanceItem: {
    gap: 8,
  },
  circleMaintenanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  circleIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  circleMaintenanceInfo: {
    flex: 1,
  },
  circleName: {
    fontWeight: '500',
  },
  circleStats: {
    opacity: 0.7,
  },
  scoreText: {
    fontWeight: 'bold',
  },
  maintenanceProgressBar: {
    height: 6,
    borderRadius: 3,
  },
  typeAnalysisContainer: {
    gap: 16,
  },
  typeItem: {
    gap: 8,
  },
  typeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  typeName: {
    fontWeight: '500',
  },
  typePercentage: {
    opacity: 0.7,
  },
  typeProgressBar: {
    height: 6,
    borderRadius: 3,
  },
  preferredChip: {
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  preferredChipText: {
    fontSize: 12,
  },
  trendContainer: {
    gap: 16,
  },
  trendChart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 100,
    paddingHorizontal: 8,
  },
  trendBar: {
    alignItems: 'center',
    gap: 4,
    flex: 1,
  },
  trendBarFill: {
    width: 20,
    borderRadius: 2,
    minHeight: 4,
  },
  trendBarLabel: {
    opacity: 0.7,
  },
  trendBarCount: {
    fontWeight: '500',
  },
  trendInsight: {
    padding: 12,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  trendInsightText: {
    textAlign: 'center',
  },
  insightsContainer: {
    gap: 12,
  },
  insightItem: {
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  insightTitle: {
    fontWeight: 'bold',
  },
  insightMessage: {
    opacity: 0.8,
  },
  insightSuggestion: {
    opacity: 0.7,
    fontStyle: 'italic',
  },
});
